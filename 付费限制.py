#此脚本在服务端运行，用于处理付费限制
import datetime
import logging
import time
import threading
import os
import json
import random
import string
from flask import Flask, request, jsonify
import requests
import hashlib
import base64
import struct
import mysql.connector
from mysql.connector import pooling


# 配置日志
from logging.handlers import RotatingFileHandler

# 创建日志格式
log_formatter = logging.Formatter('%(asctime)s - %(levelname)s - [%(threadName)s] - %(filename)s:%(lineno)d - %(message)s')

# 创建自定义过滤器，只允许特定关键日志通过
class 关键日志过滤器(logging.Filter):
    def filter(self, record):
        # 警告、错误和严重错误级别始终通过
        if record.levelno >= logging.WARNING:
            return True

        # 特定的关键信息性日志也允许通过
        if record.levelno == logging.INFO:
            关键信息 = [
                "成功获取", "验证成功", "验证失败", "服务启动",
                "已删除", "调用频率限制", "号池已关闭", "版本过低",
                "卡密不存在", "卡密已到期", "卡密已被禁用", "账号授权检查"
            ]

            # 如果日志消息包含任何关键词，允许通过
            for 关键词 in 关键信息:
                if 关键词 in record.getMessage():
                    return True

            # 其他INFO级别日志不通过
            return False

        # 其他级别（如DEBUG）默认不通过
        return False

# 创建控制台处理器
console_handler = logging.StreamHandler()
console_handler.setFormatter(log_formatter)

# 创建文件处理器（带轮转功能）
file_handler = RotatingFileHandler(
    filename='付费限制.log',
    mode='w',  # 修改为'w'模式，每次启动时覆盖之前的日志
    maxBytes=10485760,  # 10MB
    backupCount=5,
    encoding='utf-8'
)
file_handler.setFormatter(log_formatter)

# 配置根日志记录器
root_logger = logging.getLogger()
root_logger.setLevel(logging.INFO)  # 保持INFO级别，但通过过滤器控制
root_logger.addHandler(console_handler)
root_logger.addHandler(file_handler)

# 添加自定义过滤器
root_logger.addFilter(关键日志过滤器())

# 数据库配置
数据库配置 = {
    'host': 'localhost',
    'port': 3306,
    'user': 'root',
    'password': 'Yuyu6709.',  # 请替换为您的实际密码
    'database': 'kami3162', # 请确保数据库名正确
    'pool_name': 'mysql_paid_pool',
    'pool_size': 32,
    'pool_reset_session': True,
    'autocommit': True,
    'connection_timeout': 10,
    'use_pure': True,
    'get_warnings': True,
}

# 缓存管理器类
class 缓存管理器:
    def __init__(self, 默认过期时间=300):
        self.缓存 = {}
        self.过期时间 = {}
        self.默认过期时间 = 默认过期时间
        self.锁 = threading.RLock()
        self.启动清理线程()
        logging.info(f"缓存管理器初始化，默认过期时间: {默认过期时间}秒")

    def get(self, key, default=None):
        with self.锁:
            if key in self.缓存 and (key not in self.过期时间 or self.过期时间[key] > time.time()):
                return self.缓存[key]
            return default

    def set(self, key, value, ttl=None):
        with self.锁:
            self.缓存[key] = value
            if ttl is None:
                ttl = self.默认过期时间
            if ttl > 0:
                self.过期时间[key] = time.time() + ttl
            return True

    def delete(self, key):
        with self.锁:
            if key in self.缓存:
                del self.缓存[key]
            if key in self.过期时间:
                del self.过期时间[key]
            return True

    def clear(self, pattern=None):
        with self.锁:
            if pattern is None:
                self.缓存.clear()
                self.过期时间.clear()
                return True

            keys_to_delete = [k for k in self.缓存.keys() if pattern in k]
            for key in keys_to_delete:
                self.delete(key)
            return True

    def 清理过期缓存(self):
        with self.锁:
            当前时间 = time.time()
            过期键 = [k for k, v in self.过期时间.items() if v <= 当前时间]
            for key in 过期键:
                self.delete(key)
            return len(过期键)

    def 启动清理线程(self, 间隔=60):
        def 清理任务():
            while True:
                time.sleep(间隔)
                try:
                    清理数量 = self.清理过期缓存()
                    if 清理数量 > 0:
                        logging.debug(f"缓存清理: 已删除 {清理数量} 个过期项")
                except Exception as e:
                    logging.error(f"缓存清理任务出错: {e}", exc_info=True)

        清理线程 = threading.Thread(target=清理任务, name="CacheCleanupThread_Paid", daemon=True)
        清理线程.start()
        logging.info(f"缓存定期清理任务已启动，清理间隔: {间隔}秒。")

# 数据库连接管理器类
class 数据库连接管理器:
    def __init__(self, db_config, 缓存管理器实例=None):
        self.db_config = db_config
        self.缓存 = 缓存管理器实例
        try:
            self.pool = pooling.MySQLConnectionPool(**self.db_config)
            logging.info(f"数据库连接池 '{db_config['pool_name']}' 初始化成功")
        except Exception as e:
            logging.error(f"数据库连接池 '{db_config['pool_name']}' 初始化失败: {e}", exc_info=True)
            raise

    def get_connection(self):
        try:
            return self.pool.get_connection()
        except Exception as e:
            logging.error(f"获取数据库连接失败: {e}", exc_info=True)
            raise

    def execute_query(self, query, params=None, cache_key=None, cache_ttl_seconds=None):
        if cache_key and self.缓存:
            cached_result = self.缓存.get(cache_key)
            if cached_result is not None:
                return cached_result

        conn = None
        cursor = None
        try:
            conn = self.get_connection()
            cursor = conn.cursor(dictionary=True)
            cursor.execute(query, params)
            result = cursor.fetchall()
            if cache_key and self.缓存:
                self.缓存.set(cache_key, result, cache_ttl_seconds)
            return result
        except Exception as e:
            logging.error(f"执行查询失败: {e}, 查询: {query}, 参数: {params}", exc_info=True)
            raise
        finally:
            if cursor:
                cursor.close()
            if conn:
                conn.close()

    def execute_single_query(self, query, params=None, cache_key=None, cache_ttl_seconds=None):
        results = self.execute_query(query, params, cache_key, cache_ttl_seconds)
        return results[0] if results else None

    def execute_update(self, query, params=None, clear_cache_keys=None):
        conn = None
        cursor = None
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            cursor.execute(query, params)
            affected_rows = cursor.rowcount
            if clear_cache_keys and self.缓存:
                if isinstance(clear_cache_keys, list):
                    for key in clear_cache_keys:
                        self.缓存.delete(key)
                else:
                    self.缓存.delete(clear_cache_keys)
            return affected_rows
        except Exception as e:
            logging.error(f"执行更新失败: {e}, 查询: {query}, 参数: {params}", exc_info=True)
            raise
        finally:
            if cursor:
                cursor.close()
            if conn:
                conn.close()

# 创建Flask应用
app = Flask(__name__)


# 付费账号特定配置
付费账号模块配置 = {
    '卡密信息缓存秒数': 3,  # 改为3秒
    '每次获取消耗额度': 50,  # 每次获取账号消耗的额度
    '邮箱后缀': 'gmail***'  # 虚假邮箱使用的后缀
}

# 初始化代理服务器表结构
def 初始化代理服务器表():
    """初始化代理服务器表结构，确保包含所需字段"""
    try:
        # 临时创建数据库连接来检查表结构
        temp_conn = mysql.connector.connect(**数据库配置)
        cursor = temp_conn.cursor()

        # 检查表是否存在
        cursor.execute("SHOW TABLES LIKE '代理服务器表'")
        table_exists = cursor.fetchone()

        if not table_exists:
            logging.info("代理服务器表不存在，创建新表...")

            # 创建新表
            create_table_sql = """
            CREATE TABLE `代理服务器表` (
                `id` int NOT NULL AUTO_INCREMENT,
                `代理服务器` varchar(255) NOT NULL COMMENT '代理服务器地址',
                `最大数量` int NOT NULL DEFAULT 0 COMMENT '该代理服务器可以分配的最大用户数',
                `已使用数量` int NOT NULL DEFAULT 0 COMMENT '当前已分配给用户的数量',
                `创建时间` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '代理服务器创建时间',
                `到期时间` datetime NOT NULL COMMENT '代理服务器的到期时间',
                `状态` tinyint NOT NULL DEFAULT 1 COMMENT '状态：1启用，0禁用',
                `备注` varchar(500) DEFAULT NULL COMMENT '备注信息',
                PRIMARY KEY (`id`),
                UNIQUE KEY `代理服务器` (`代理服务器`),
                KEY `idx_expiry_status` (`到期时间`, `状态`),
                KEY `idx_usage` (`已使用数量`, `最大数量`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='代理服务器管理表'
            """

            cursor.execute(create_table_sql)
            logging.info("成功创建代理服务器表")
        else:
            logging.info("代理服务器表已存在")

        cursor.close()
        temp_conn.close()
        logging.info("代理服务器表结构检查完成")
        return True

    except Exception as e:
        logging.error(f"初始化代理服务器表失败: {e}", exc_info=True)
        return False

# 初始化卡密系统表的代理服务器字段
def 初始化卡密系统表代理字段():
    """确保卡密系统表包含代理服务器相关字段"""
    try:
        # 临时创建数据库连接来检查表结构
        temp_conn = mysql.connector.connect(**数据库配置)
        cursor = temp_conn.cursor()

        # 获取现有表结构
        cursor.execute("DESCRIBE 卡密系统")
        existing_columns = {}
        for row in cursor.fetchall():
            if len(row) >= 2:
                existing_columns[str(row[0])] = str(row[1])

        # 需要的字段定义
        required_columns = {
            '代理服务器': 'varchar(255) DEFAULT NULL COMMENT "关联的代理服务器地址"',
            '代理服务器到期时间': 'datetime DEFAULT NULL COMMENT "关联的代理服务器的到期时间"'
        }

        # 检查并添加缺失的字段
        for column_name, column_definition in required_columns.items():
            if column_name not in existing_columns:
                try:
                    alter_sql = f"ALTER TABLE 卡密系统 ADD COLUMN `{column_name}` {column_definition}"
                    cursor.execute(alter_sql)
                    logging.info(f"成功添加字段: {column_name}")
                except Exception as e:
                    logging.warning(f"添加字段 {column_name} 失败: {e}")

        cursor.close()
        temp_conn.close()
        logging.info("卡密系统表代理字段检查完成")
        return True

    except Exception as e:
        logging.error(f"初始化卡密系统表代理字段失败: {e}", exc_info=True)
        return False

# 创建缓存和数据库连接实例
缓存实例 = 缓存管理器(默认过期时间=付费账号模块配置['卡密信息缓存秒数'])
数据库实例 = 数据库连接管理器(数据库配置, 缓存实例)

# 初始化数据库表结构
try:
    初始化代理服务器表()
    初始化卡密系统表代理字段()
except Exception as e:
    logging.warning(f"数据库表初始化警告: {e}")  # 不阻止服务启动


class 付费账号管理器:
    """付费账号相关功能管理"""

    def __init__(self, 数据库管理器: 数据库连接管理器, 缓存管理器_instance: 缓存管理器):
        self.数据库 = 数据库管理器
        self.缓存 = 缓存管理器_instance

    def 获取账号(self, 用户标识, 是卡密=True):
        """获取账号，使用真实令牌但生成虚假邮箱"""
        try:
            # 获取真实令牌
            邮箱信息 = self.数据库.execute_single_query(
                """
                SELECT 邮箱, 访问令牌, 刷新令牌
                FROM 邮箱系统
                WHERE 邮箱 IS NOT NULL
                AND 访问令牌 IS NOT NULL
                AND 刷新令牌 IS NOT NULL
                ORDER BY 创建时间 ASC
                LIMIT 1
                """
            )

            if not 邮箱信息:
                logging.error(f"无可用付费账号: {用户标识}")
                return False, None, None, None, '当前独享号池资源紧张，请稍后再试'

            访问令牌 = 邮箱信息['访问令牌']
            刷新令牌 = 邮箱信息['刷新令牌']

            # 生成虚假邮箱
            import random
            import string
            字符集 = string.ascii_letters + string.digits
            随机字符串 = ''.join(random.choice(字符集) for _ in range(8))
            虚假邮箱 = f"{随机字符串}@{付费账号模块配置['邮箱后缀']}"

            # 将邮箱信息写入到已删除邮箱表中 - 使用真实邮箱记录
            self.数据库.execute_update(
                "INSERT IGNORE INTO 已删除邮箱(邮箱, 访问令牌, 刷新令牌, 创建时间, 用户标识, 删除原因) VALUES (%s, %s, %s, NOW(), %s, %s)",
                params=(邮箱信息['邮箱'], 访问令牌, 刷新令牌, 用户标识, "正常使用")
            )

            # 从邮箱系统表中删除
            self.数据库.execute_update(
                "DELETE FROM 邮箱系统 WHERE 邮箱 = %s",
                (邮箱信息['邮箱'],)
            )

            logging.info(f"用户 {用户标识} 成功获取虚假账号: {虚假邮箱}")
            return True, 虚假邮箱, 访问令牌, 刷新令牌, None

        except Exception as e:
            logging.error(f"获取账号出错: {e}", exc_info=True)
            return False, None, None, None, "系统繁忙，请稍后重试"

    def 检查IP是否被永久禁用(self, IP):
        """检查IP是否被永久禁用"""
        try:
            # 检查是否在永久黑名单中
            permanent_ban_key = f"permanent_ban:{IP}"
            if self.缓存.get(permanent_ban_key):
                logging.warning(f"永久封禁IP访问: {IP}")
                return False, "系统检测到异常行为，相关信息已记录"
            return True, "IP检查通过"
        except Exception as e:
            logging.error(f"IP禁用检查失败: {e}", exc_info=True)
            return True, "IP检查通过"  # 出错时不阻止正常使用

    def 检查同秒访问并禁用IP(self, IP, 当前时间, 卡密):
        """检查同秒访问并可能禁用IP - 同一秒内访问3次自动永久禁用IP"""
        try:
            # 生成同秒访问记录的缓存键
            当前秒 = 当前时间.strftime('%Y-%m-%d %H:%M:%S')
            同秒访问键 = f"same_second_access:{IP}:{当前秒}"

            # 获取当前秒的访问次数
            访问次数 = self.缓存.get(同秒访问键) or 0
            访问次数 += 1

            # 设置缓存，过期时间1秒
            self.缓存.set(同秒访问键, 访问次数, 1)

            # 如果同一秒内访问8次或更多，永久禁用IP
            if 访问次数 >= 8:
                # 永久禁用IP
                permanent_ban_key = f"permanent_ban:{IP}"
                self.缓存.set(permanent_ban_key, True, 86400 * 365)  # 1年

                logging.error(f"IP {IP} 因同秒访问{访问次数}次被永久禁用，卡密: {卡密}")
                return False, "系统检测到异常行为，相关信息已记录"

            return True, "访问检查通过"

        except Exception as e:
            logging.error(f"同秒访问检查失败: {e}", exc_info=True)
            return True, "访问检查通过"  # 出错时不阻止正常使用



    def 检查每日获取次数限制(self, 卡密, 今日获取次数):
        """检查每日获取次数是否超过限制"""
        try:
            每日最大获取次数 = 30  # 每日最大获取30次

            if 今日获取次数 >= 每日最大获取次数:
                logging.warning(f"卡密 {卡密} 今日获取次数已达到限制: {今日获取次数}/{每日最大获取次数}")
                return False

            return True

        except Exception as e:
            logging.error(f"检查卡密 {卡密} 每日获取次数限制失败: {e}", exc_info=True)
            return True  # 出错时不阻止正常使用

    def 检查调用限制(self, 卡密信息):
        """检查卡密调用限制 - 已移除时间限制"""
        try:
            当前时间 = datetime.datetime.now()
            卡密 = 卡密信息.get('卡密')

            # 不再检查十分钟和二十分钟的限制
            # 直接返回成功，允许获取账号
            return True, None

        except Exception as e:
            logging.error(f"检查卡密 {卡密信息.get('卡密')} 调用限制失败: {e}", exc_info=True)
            return True, None

    def 获取虚假付费时长(self):
        """
        从数据库获取虚假付费时长配置（分钟）
        """
        try:
            # 从cursorpro表获取虚假付费时长配置（不使用缓存，立即生效）
            配置 = self.数据库.execute_single_query(
                "SELECT 虚假付费时长 FROM cursorpro ORDER BY id DESC LIMIT 1"
            )

            if 配置 and '虚假付费时长' in 配置:
                虚假时长 = 配置['虚假付费时长']
                # 确保是有效的整数
                if isinstance(虚假时长, int) and 虚假时长 >= 0:
                    logging.debug(f"获取到虚假付费时长配置: {虚假时长} 分钟")
                    return 虚假时长
                else:
                    logging.warning(f"虚假付费时长配置无效: {虚假时长}，使用默认值5分钟")
                    return 5
            else:
                logging.warning("未找到虚假付费时长配置，使用默认值5分钟")
                return 5

        except Exception as e:
            logging.error(f"获取虚假付费时长配置失败: {e}，使用默认值5分钟")
            return 5

    def 生成虚假账号信息(self):
        """生成虚假账号信息"""
        # 生成虚假邮箱：8-12位随机字符@配置的邮箱后缀
        用户名 = ''.join(random.choices(string.ascii_lowercase + string.digits, k=random.randint(8, 12)))
        虚假邮箱 = f"{用户名}@{付费账号模块配置['邮箱后缀']}"

        # 生成虚假令牌：固定前缀 + 随机字符，保持长度一致
        前缀 = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9."
        中间部分 = ''.join(random.choices(string.ascii_letters + string.digits + '_-', k=200))
        后缀 = ''.join(random.choices(string.ascii_letters + string.digits + '_-', k=43))
        虚假访问令牌 = f"{前缀}{中间部分}.{后缀}"
        虚假刷新令牌 = f"{前缀}{中间部分}.{后缀}"

        return {"邮箱": 虚假邮箱, "访问令牌": 虚假访问令牌, "刷新令牌": 虚假刷新令牌}

    def 检查是否需要付费虚假获取(self, 卡密, 当前时间, 卡密信息):
        """
        检查是否需要付费虚假获取（基于最近登录时间和虚假付费时长配置）
        """
        try:
            # 从数据库获取虚假付费时长配置
            虚假付费时长_分钟 = self.获取虚假付费时长()
            if 虚假付费时长_分钟 <= 0:
                logging.debug(f"虚假付费时长配置为 {虚假付费时长_分钟} 分钟，禁用虚假获取")
                return False

            # 获取最近登录时间
            最近登录 = 卡密信息.get('最近登录')

            # 如果没有最近登录时间，说明是首次获取，不需要虚假获取
            if not 最近登录:
                logging.debug(f"卡密 {卡密} 无最近登录时间，不需要虚假获取")
                return False

            # 解析最近登录时间
            try:
                if isinstance(最近登录, str):
                    最近登录时间 = datetime.datetime.strptime(最近登录, '%Y-%m-%d %H:%M:%S')
                elif hasattr(最近登录, 'strftime'):
                    # 如果是datetime对象，直接使用
                    最近登录时间 = 最近登录
                else:
                    logging.warning(f"卡密 {卡密} 最近登录时间格式无效: {最近登录}，不需要虚假获取")
                    return False

            except Exception as e:
                logging.warning(f"解析卡密 {卡密} 最近登录时间失败: {e}，不需要虚假获取")
                return False

            # 计算时间差
            时间差 = 当前时间 - 最近登录时间
            虚假时长_秒 = 虚假付费时长_分钟 * 60  # 转换为秒

            # 如果在虚假时长内，则需要虚假获取
            if 时间差.total_seconds() <= 虚假时长_秒:
                logging.info(f"卡密 {卡密} 距离最近登录 {时间差.total_seconds():.1f} 秒，在{虚假付费时长_分钟}分钟内，需要虚假获取")
                return True
            else:
                logging.debug(f"卡密 {卡密} 距离最近登录 {时间差.total_seconds():.1f} 秒，超过{虚假付费时长_分钟}分钟，不需要虚假获取")
                return False

        except Exception as e:
            logging.error(f"检查是否需要付费虚假获取时出错: {e}")
            return False

    def 检查卡密有效性(self, 卡密, 客户端IP=None):
        """检查卡密是否有效"""
        try:
            当前时间 = datetime.datetime.now()

            # 如果提供了IP，进行IP相关检查
            if 客户端IP:
                # 检查IP是否被永久禁用
                ip_check_result, ip_message = self.检查IP是否被永久禁用(客户端IP)
                if not ip_check_result:
                    return False, ip_message, None

                # 检查同秒访问并可能禁用IP
                同秒检查结果, 同秒消息 = self.检查同秒访问并禁用IP(客户端IP, 当前时间, 卡密)
                if not 同秒检查结果:
                    return False, 同秒消息, None

            缓存键 = f"卡密信息:{卡密}"
            卡密信息 = self.缓存.get(缓存键)
            if not 卡密信息:
                卡密信息 = self.数据库.execute_single_query(
                    "SELECT * FROM 卡密系统 WHERE 卡密=%s", (卡密,)
                )
                if 卡密信息:
                    # 使用模块配置中的缓存时间
                    self.缓存.set(缓存键, 卡密信息, 付费账号模块配置['卡密信息缓存秒数'])

            if not 卡密信息:
                logging.info(f"付费卡密不存在: {卡密}")
                return False, "卡密不存在", None



            禁用状态 = 卡密信息.get('禁用卡密')
            if 禁用状态 is not None and int(禁用状态) > 0:
                # 卡密被禁用，不自动解封，需要联系管理员
                logging.info(f"付费卡密被禁用: {卡密}")
                return False, "系统检测到刷号异常使用行为，已被禁用，请联系管理员处理", None

            到期时间 = 卡密信息.get('到期时间')
            if isinstance(到期时间, str):
                 到期时间 = datetime.datetime.strptime(到期时间, '%Y-%m-%d %H:%M:%S')
            if 到期时间 and 当前时间 > 到期时间:
                logging.info(f"付费卡密已到期: {卡密}, 到期时间: {到期时间}")
                # 到期时间字符串 = 到期时间.strftime('%Y-%m-%d %H:%M')
                return False, f"今日免费额度紧张已用完，立即升级获取独享无限额度？", None

            # 检查剩余额度
            剩余额度 = 卡密信息.get('剩余额度', 0)
            # 剩余额度本身就是int类型，不需要类型转换

            # 使用配置中的消耗额度值
            消耗额度 = 付费账号模块配置['每次获取消耗额度']

            if 剩余额度 < 消耗额度:
                logging.warning(f"付费卡密 {卡密} 剩余额度不足: {剩余额度}")
                return False, "存到大量刷号行为而未正常使用，请重新购买额度", None

            # 检查每日获取次数限制
            今日获取次数 = 卡密信息.get('今日获取次数', 0) or 0
            if not self.检查每日获取次数限制(卡密, 今日获取次数):
                # 自动禁用卡密，因为正常使用不可能达到30次
                self.数据库.execute_update(
                    "UPDATE 卡密系统 SET 禁用卡密=1 WHERE 卡密=%s",
                    (卡密,)
                )
                self.缓存.clear(f"卡密信息:{卡密}")  # 清除缓存
                logging.warning(f"付费卡密 {卡密} 因每日获取次数超过30次被自动禁用")
                return False, "系统检测到刷号异常使用行为，已被禁用，请联系管理员处理", None

            # 先检查是否需要虚假获取（虚假获取不需要消耗额度和更新记录）
            虚假获取检查结果 = self.检查是否需要付费虚假获取(卡密, 当前时间, 卡密信息)
            if 虚假获取检查结果:
                logging.info(f"卡密 {卡密} 触发虚假获取条件，返回虚假成功，不更新数据库")
                # 返回虚假成功，不消耗额度，不更新访问记录
                虚假账号信息 = self.生成虚假账号信息()
                # 返回成功，但在卡密详情中标记为虚假获取
                虚假卡密详情 = dict(卡密信息)
                虚假卡密详情['is_fake'] = True  # 标记为虚假获取
                return True, 虚假账号信息, self._serialize_card_info(虚假卡密详情)



            result, _ = self.检查调用限制(卡密信息)
            if result:
                # 获取账号
                获取成功, 邮箱, 访问令牌, 刷新令牌, 错误类型 = self.获取账号(卡密, True)

                if 获取成功:
                    # 更新卡密使用记录（包括访问时间记录）
                    self.更新卡密使用记录(卡密, 剩余额度)

                    新剩余额度 = max(0, 剩余额度 - 消耗额度)  # 计算新的剩余额度

                    # 创建一个包含更新后剩余额度的卡密信息副本
                    更新后卡密信息_返回 = dict(卡密信息)
                    更新后卡密信息_返回['剩余额度'] = 新剩余额度

                    return True, {"邮箱": 邮箱, "访问令牌": 访问令牌, "刷新令牌": 刷新令牌}, self._serialize_card_info(更新后卡密信息_返回)
                else:
                    logging.warning(f"付费卡密 {卡密} 获取账号失败: {错误类型}")
                    # 此处不应返回卡密信息，因为账号获取失败，卡密本身可能仍有效但无账号可用
                    return False, 错误类型, None
            else:
                # 由于已移除时间限制，这段代码不应该被执行，但为了安全保留
                错误消息 = f"卡密验证失败"
                logging.error(f"付费卡密 {卡密} 验证失败: 意外错误")
                return False, 错误消息, None

        except Exception as e:
            logging.error(f"检查付费卡密有效性失败 ({卡密}): {e}", exc_info=True)
            return False, "系统繁忙，请稍后重试", None

    def _serialize_card_info(self, card_info_dict):
        if not card_info_dict: return None
        return {k: (v.strftime('%Y-%m-%d %H:%M:%S') if isinstance(v, datetime.datetime) else v) for k, v in card_info_dict.items()}

    def 更新卡密使用记录(self, 卡密, 剩余额度):
        """更新卡密使用记录 - 已移除时间限制相关字段，增加剩余额度扣减，更新访问时间记录"""
        try:
            当前时间 = datetime.datetime.now()

            # 使用配置中的消耗额度值
            消耗额度 = 付费账号模块配置['每次获取消耗额度']

            # 计算新的剩余额度
            新剩余额度 = max(0, 剩余额度 - 消耗额度)  # 确保不会变成负数

            # 获取当前卡密信息，检查访问时间记录和今日获取次数
            卡密信息 = self.数据库.execute_single_query(
                "SELECT 访问时间记录, 今日获取次数 FROM 卡密系统 WHERE 卡密=%s",
                params=(卡密,)
            )

            # 更新访问时间记录
            访问时间记录 = 卡密信息.get('访问时间记录', '') if 卡密信息 else ''
            访问时间列表 = []
            if 访问时间记录:
                try:
                    时间字符串列表 = 访问时间记录.split(',')
                    for 时间字符串 in 时间字符串列表:
                        if 时间字符串.strip():
                            访问时间列表.append(时间字符串.strip())
                except:
                    访问时间列表 = []

            # 计算今日获取次数 - 基于访问时间记录判断
            今天 = 当前时间.date()
            今日获取次数 = 1  # 默认为1（当前这次获取）

            if 卡密信息:
                当前今日获取次数 = 卡密信息.get('今日获取次数', 0) or 0

                # 通过访问时间记录判断最近一次获取是否是今天
                if 访问时间列表:
                    try:
                        # 获取最近一次访问时间（不包括当前这次，因为还没添加到列表中）
                        最近访问时间字符串 = 访问时间列表[-1]
                        最近访问时间 = datetime.datetime.strptime(最近访问时间字符串, '%Y-%m-%d %H:%M:%S')
                        最近访问日期 = 最近访问时间.date()

                        if 最近访问日期 == 今天:
                            # 同一天，递增今日获取次数
                            今日获取次数 = 当前今日获取次数 + 1
                        else:
                            # 如果不是同一天，今日获取次数重置为1
                            今日获取次数 = 1
                    except:
                        # 解析失败时，保持默认值1
                        pass
                else:
                    # 如果没有访问时间记录，检查当前今日获取次数是否需要重置
                    # 这种情况下我们无法判断上次访问时间，保守起见递增计数
                    今日获取次数 = 当前今日获取次数 + 1

            # 添加当前访问时间
            访问时间列表.append(当前时间.strftime('%Y-%m-%d %H:%M:%S'))
            新访问时间记录 = ','.join(访问时间列表)

            # 更新卡密使用记录、访问时间记录和今日获取次数
            self.数据库.execute_update(
                """
                UPDATE 卡密系统
                SET 总调用次数=总调用次数+1,
                    最近登录=%s, 本月登录次数=本月登录次数+1,
                    剩余额度=%s, 访问时间记录=%s, 今日获取次数=%s
                WHERE 卡密=%s
                """,
                params=(当前时间, 新剩余额度, 新访问时间记录, 今日获取次数, 卡密)
            )
            self.缓存.clear(f"卡密信息:{卡密}")
            logging.debug(f"付费卡密 {卡密} 使用记录已更新，剩余额度从 {剩余额度} 减至 {新剩余额度}，今日获取次数: {今日获取次数}，访问时间记录: {新访问时间记录}，缓存已清除。")

        except Exception as e:
            logging.error(f"更新付费卡密 {卡密} 使用记录失败: {e}", exc_info=True)

# 创建付费账号管理器实例 - 移到全局
付费账号管理器_实例 = 付费账号管理器(数据库管理器=数据库实例, 缓存管理器_instance=缓存实例)

@app.route('/api/paid', methods=['POST'])
def 付费验证路由():
    """付费卡密验证API端点"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'message': '缺少请求数据'}), 400

        # 验证必填参数
        必填参数 = ['key', 'version', 'platform']
        缺少参数 = [param for param in 必填参数 if param not in data]
        if 缺少参数:
            # 日志中记录真实的缺少参数信息，便于调试
            logging.warning(f"付费卡密验证请求缺少参数: {', '.join(缺少参数)}")
            # 给客户端返回通用错误消息，避免暴露具体参数名称
            return jsonify({'success': False, 'message': '请求参数不完整'}), 400

        # 先获取卡密的到期时间（无论验证成功失败都返回）
        卡密到期时间 = None
        try:
            卡密基本信息 = 数据库实例.execute_single_query(
                "SELECT 到期时间 FROM 卡密系统 WHERE 卡密=%s", (data['key'],)
            )
            if 卡密基本信息 and '到期时间' in 卡密基本信息:
                到期时间_值 = 卡密基本信息['到期时间']
                if isinstance(到期时间_值, str):
                    卡密到期时间 = 到期时间_值
                elif hasattr(到期时间_值, 'strftime'):
                    卡密到期时间 = 到期时间_值.strftime('%Y-%m-%d %H:%M:%S')
        except Exception as e:
            logging.warning(f"获取卡密到期时间失败: {e}")

        # 检查系统配置
        配置 = 数据库实例.execute_single_query(
            "SELECT 开启付费号池, 网络版本号_Windows, 网络版本号_Mac FROM cursorpro ORDER BY id DESC LIMIT 1",
            cache_key="系统配置_全局", cache_ttl_seconds=60
        )

        开启付费号池 = 配置.get('开启付费号池', 0) if 配置 else 0
        网络版本号_Windows = 配置.get('网络版本号_Windows', '') if 配置 else ''
        网络版本号_Mac = 配置.get('网络版本号_Mac', '') if 配置 else ''

        客户端版本 = data['version']
        平台 = data['platform']
        服务器版本 = 网络版本号_Windows if 平台 == 'Windows' else 网络版本号_Mac

        # 检查版本
        if 服务器版本 and 客户端版本 < 服务器版本:
            response_data = {
                'success': False,
                'message': f'当前版本过低，请更新到最新版本 {服务器版本}',
                'need_update': True,
                'latest_version': 服务器版本
            }
            # 如果有到期时间，添加到响应中
            if 卡密到期时间:
                response_data['expiry_time'] = 卡密到期时间
                logging.info(f"版本检查不通过，但仍返回到期时间: {卡密到期时间}")
            return jsonify(response_data)

        # 检查付费号池状态
        if 开启付费号池 == 0:
            response_data = {
                'success': False,
                'message': '当前独享号池资源紧张，请稍后再试'
            }
            # 如果有到期时间，添加到响应中
            if 卡密到期时间:
                response_data['expiry_time'] = 卡密到期时间
                logging.info(f"付费号池关闭，但仍返回到期时间: {卡密到期时间}")
            return jsonify(response_data)

        # 记录请求信息
        logging.info(f"付费卡密验证请求 - 卡密: {data['key'][:8]}***, 平台: {平台}, 版本: {客户端版本}")

        # 验证卡密并获取账号（传递客户端IP进行检查）
        客户端IP = request.remote_addr
        result, message_or_account, 卡密详情 = 付费账号管理器_实例.检查卡密有效性(data['key'], 客户端IP)

        if result:
            # 检查是否为虚假获取
            是虚假获取 = 卡密详情 and 卡密详情.get('is_fake', False)

            if 是虚假获取:
                # 虚假获取，返回成功但标记为虚假
                logging.info(f"付费卡密虚假获取 - 卡密: {data['key'][:8]}***")
                response_data = {
                    'success': True,
                    'message': "付费卡密验证成功",
                    'is_fake': True,  # 标记为虚假获取
                    'cost_info': {
                        'cost_per_account': 付费账号模块配置['每次获取消耗额度']
                    }
                }
            else:
                # 真实获取成功
                logging.info(f"付费卡密验证成功 - 卡密: {data['key'][:8]}***")
                response_data = {
                    'success': True,
                    'message': "付费卡密验证成功",
                    'account_info': message_or_account,
                    'is_fake': False,  # 标记为真实获取
                    'cost_info': {
                        'cost_per_account': 付费账号模块配置['每次获取消耗额度']
                    }
                }

            # 无论成功失败都返回到期时间（如果存在）
            if 卡密到期时间:
                response_data['expiry_time'] = 卡密到期时间
                logging.info(f"付费卡密验证成功，返回到期时间: {卡密到期时间}")

            return jsonify(response_data)
        else:
            logging.info(f"付费卡密验证失败 - 卡密: {data['key'][:8]}***, 原因: {message_or_account}")
            response_data = {
                'success': False,
                'message': message_or_account,
                'cost_info': {
                    'cost_per_account': 付费账号模块配置['每次获取消耗额度']
                }
            }
            if 卡密详情:
                response_data['key_info'] = 卡密详情
                response_data['cost_info']['remaining_balance'] = 卡密详情.get('剩余额度', 0)

            # 无论成功失败都返回到期时间（如果存在）
            if 卡密到期时间:
                response_data['expiry_time'] = 卡密到期时间
                logging.info(f"付费卡密验证失败，但仍返回到期时间: {卡密到期时间}")

            return jsonify(response_data)

    except Exception as e:
        logging.error(f"付费验证请求处理失败: {e}", exc_info=True)
        return jsonify({'success': False, 'message': '系统繁忙，请稍后重试'}), 500

@app.route('/api/check_key', methods=['POST'])
def 卡密检查路由():
    """仅检查卡密有效性的API端点，不获取账号但是消耗额度"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'message': '缺少请求数据'}), 400

        # 验证必填参数
        必填参数 = ['key', 'version', 'platform']
        缺少参数 = [param for param in 必填参数 if param not in data]
        if 缺少参数:
            logging.warning(f"卡密检查请求缺少参数: {', '.join(缺少参数)}")
            return jsonify({'success': False, 'message': '请求参数不完整'}), 400

        卡密 = data['key']
        客户端版本 = data['version']
        平台 = data['platform']

        # 记录请求信息
        logging.info(f"卡密检查请求 - 卡密: {卡密[:8]}***, 平台: {平台}, 版本: {客户端版本}")

        # 获取卡密的到期时间
        卡密到期时间 = None
        卡密信息 = 数据库实例.execute_single_query(
            "SELECT * FROM 卡密系统 WHERE 卡密=%s", (卡密,)
        )

        if not 卡密信息:
            logging.info(f"卡密检查失败 - 卡密不存在: {卡密[:8]}***")
            return jsonify({
                'success': False,
                'message': '卡密不存在'
            })

        # 获取到期时间
        到期时间_值 = 卡密信息.get('到期时间')
        if 到期时间_值:
            if isinstance(到期时间_值, str):
                卡密到期时间 = 到期时间_值
            elif hasattr(到期时间_值, 'strftime'):
                卡密到期时间 = 到期时间_值.strftime('%Y-%m-%d %H:%M:%S')

        # 检查系统配置
        配置 = 数据库实例.execute_single_query(
            "SELECT 开启付费号池, 网络版本号_Windows, 网络版本号_Mac FROM cursorpro ORDER BY id DESC LIMIT 1",
            cache_key="系统配置_全局", cache_ttl_seconds=60
        )

        开启付费号池 = 配置.get('开启付费号池', 0) if 配置 else 0
        网络版本号_Windows = 配置.get('网络版本号_Windows', '') if 配置 else ''
        网络版本号_Mac = 配置.get('网络版本号_Mac', '') if 配置 else ''

        服务器版本 = 网络版本号_Windows if 平台 == 'Windows' else 网络版本号_Mac

        # 检查版本
        if 服务器版本 and 客户端版本 < 服务器版本:
            response_data = {
                'success': False,
                'message': f'当前版本过低，请更新到最新版本 {服务器版本}',
                'need_update': True,
                'latest_version': 服务器版本
            }
            if 卡密到期时间:
                response_data['expiry_time'] = 卡密到期时间
            return jsonify(response_data)

        # 检查付费号池状态
        if 开启付费号池 == 0:
            response_data = {
                'success': False,
                'message': '当前独享号池资源紧张，请稍后再试'
            }
            if 卡密到期时间:
                response_data['expiry_time'] = 卡密到期时间
            return jsonify(response_data)

        # 检查卡密状态
        当前时间 = datetime.datetime.now()

        # 检查禁用状态
        禁用状态 = 卡密信息.get('禁用卡密')
        if 禁用状态 is not None and int(禁用状态) > 0:
            logging.info(f"卡密检查失败 - 卡密已禁用: {卡密[:8]}***")
            return jsonify({
                'success': False,
                'message': "系统检测到刷号异常使用行为，已被禁用，请联系管理员处理",
                'expiry_time': 卡密到期时间 if 卡密到期时间 else None
            })

        # 检查到期状态
        到期时间 = 卡密信息.get('到期时间')
        if isinstance(到期时间, str):
            到期时间 = datetime.datetime.strptime(到期时间, '%Y-%m-%d %H:%M:%S')
        if 到期时间 and 当前时间 > 到期时间:
            logging.info(f"卡密检查失败 - 卡密已到期: {卡密[:8]}***")
            return jsonify({
                'success': False,
                'message': "今日免费额度紧张已用完，立即升级获取独享无限额度？",
                'expiry_time': 卡密到期时间 if 卡密到期时间 else None
            })

        # 检查剩余额度
        剩余额度 = 卡密信息.get('剩余额度', 0)
        消耗额度 = 付费账号模块配置['每次获取消耗额度']

        if 剩余额度 < 消耗额度:
            logging.info(f"卡密检查失败 - 卡密余额不足: {卡密[:8]}***")
            return jsonify({
                'success': False,
                'message': "存到大量刷号行为而未正常使用，请重新购买额度",
                'expiry_time': 卡密到期时间 if 卡密到期时间 else None,
                'remaining_balance': 剩余额度
            })

        # 所有检查都通过
        logging.info(f"卡密检查成功 - 卡密: {卡密[:8]}***")

        # 更新卡密使用记录（消耗额度和更新使用次数）
        付费账号管理器_实例.更新卡密使用记录(卡密, 剩余额度)

        # 计算新的剩余额度
        新剩余额度 = max(0, 剩余额度 - 消耗额度)

        return jsonify({
            'success': True,
            'message': "卡密验证成功",
            'expiry_time': 卡密到期时间 if 卡密到期时间 else None,
            'remaining_balance': 新剩余额度,  # 返回更新后的剩余额度
            'cost_per_account': 消耗额度
        })

    except Exception as e:
        logging.error(f"卡密检查失败 - 异常: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'message': "系统繁忙，请稍后重试"
        }), 500

@app.route('/api/get_proxy', methods=['POST'])
def 获取代理服务器路由():
    """获取代理服务器API端点"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'message': '请购买基础服务成为独享用户后再尝试开启'}), 400

        # 验证必填参数
        if 'key' not in data:
            return jsonify({'success': False, 'message': '请购买基础服务成为独享用户后再尝试开启'}), 400

        卡密 = data['key']

        # 记录请求信息
        logging.info(f"获取代理服务器请求 - 卡密: {卡密[:8]}***")

        # 1. 验证卡密
        卡密信息 = 数据库实例.execute_single_query(
            "SELECT * FROM 卡密系统 WHERE 卡密=%s", (卡密,)
        )

        if not 卡密信息:
            logging.info(f"获取代理服务器失败 - 卡密不存在: {卡密[:8]}***")
            return jsonify({
                'success': False,
                'message': '请购买基础服务成为独享用户后再尝试开启'
            })

        # 检查卡密到期时间
        当前时间 = datetime.datetime.now()
        到期时间 = 卡密信息.get('到期时间')
        if isinstance(到期时间, str):
            到期时间 = datetime.datetime.strptime(到期时间, '%Y-%m-%d %H:%M:%S')
        if 到期时间 and 当前时间 > 到期时间:
            logging.info(f"获取代理服务器失败 - 卡密已到期: {卡密[:8]}***")
            return jsonify({
                'success': False,
                'message': '请购买基础服务成为独享用户后再尝试开启'
            })

        # 2. 检查代理服务器到期时间
        代理服务器到期时间 = 卡密信息.get('代理服务器到期时间')
        if not 代理服务器到期时间:
            # 如果代理服务器到期时间为空，说明没有购买代理服务器服务
            logging.info(f"获取代理服务器失败 - 未购买代理服务器服务: {卡密[:8]}***")
            return jsonify({
                'success': False,
                'message': '您尚未购买专线加速服务，请先购买'
            })

        # 检查代理服务器是否已过期
        if isinstance(代理服务器到期时间, str):
            代理服务器到期时间 = datetime.datetime.strptime(代理服务器到期时间, '%Y-%m-%d %H:%M:%S')
        if 当前时间 > 代理服务器到期时间:
            logging.info(f"获取代理服务器失败 - 代理服务器已过期: {卡密[:8]}***")
            return jsonify({
                'success': False,
                'message': '代理服务器已过期，请续费后使用'
            })

        # 3. 获取代理服务器
        现有代理服务器 = 卡密信息.get('代理服务器')

        if 现有代理服务器:
            # 如果卡密已关联代理服务器，直接返回
            logging.info(f"卡密 {卡密[:8]}*** 已关联代理服务器: {现有代理服务器}")
            return jsonify({
                'success': True,
                'message': '获取专线加速服务成功',
                'proxy_server': 现有代理服务器
            })
        else:
            # 如果卡密没有关联代理服务器，需要分配一个
            # 查找可用的代理服务器
            可用代理服务器 = 数据库实例.execute_single_query(
                """
                SELECT 代理服务器, 最大数量, 已使用数量
                FROM 代理服务器表
                WHERE 到期时间 > %s
                AND 已使用数量 < 最大数量
                ORDER BY 创建时间 ASC
                LIMIT 1
                """,
                (当前时间,)
            )

            if not 可用代理服务器:
                logging.warning(f"无可用代理服务器 - 卡密: {卡密[:8]}***")
                return jsonify({
                    'success': False,
                    'message': '请联系提醒QQ群管理员获取'
                })

            选中的代理服务器 = 可用代理服务器['代理服务器']

            # 更新卡密系统表，关联代理服务器
            数据库实例.execute_update(
                "UPDATE 卡密系统 SET 代理服务器 = %s WHERE 卡密 = %s",
                (选中的代理服务器, 卡密)
            )

            # 更新代理服务器表，增加已使用数量
            数据库实例.execute_update(
                "UPDATE 代理服务器表 SET 已使用数量 = 已使用数量 + 1 WHERE 代理服务器 = %s",
                (选中的代理服务器,)
            )

            logging.info(f"为卡密 {卡密[:8]}*** 分配代理服务器: {选中的代理服务器}")
            return jsonify({
                'success': True,
                'message': '获取专线加速服务成功',
                'proxy_server': 选中的代理服务器
            })

    except Exception as e:
        logging.error(f"获取代理服务器失败 - 异常: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'message': "系统繁忙，请稍后重试"
        }), 500

# 日志中间件和主程序入口
@app.before_request
def log_request_info():
    """在每个请求处理前记录请求信息。"""
    logging.info(f"收到请求: {request.method} {request.full_path} User-Agent: {request.user_agent.string}")

@app.route('/api/prices', methods=['GET'])
def 查询价格路由():
    """查询卡片价格API端点 - 从cursorpro表动态获取价格"""
    try:
        # 从cursorpro表查询价格信息 - 禁用缓存以确保价格实时更新
        价格配置 = 数据库实例.execute_single_query(
            "SELECT 时卡, 天卡, 周卡, 月卡, 季卡, 年卡 FROM cursorpro ORDER BY id DESC LIMIT 1"
            # 不使用缓存，每次都获取最新价格
        )

        # 默认价格配置（当数据库查询失败时使用）
        默认价格配置 = {
            '时卡': 2.0,
            '天卡': 6.6,
            '周卡': 9.9,
            '月卡': 29.9,
            '季卡': 69.9,
            '年卡': 360.0
        }

        # 构建价格数组
        prices = []

        # 定义卡片类型和对应的数据库字段名
        卡片类型映射 = [
            ('时卡', '时卡'),
            ('天卡', '天卡'),
            ('周卡', '周卡'),
            ('月卡', '月卡'),
            ('季卡', '季卡'),
            ('年卡', '年卡')
        ]

        for 卡片类型, 字段名 in 卡片类型映射:
            # 优先使用数据库中的价格，如果不存在则使用默认价格
            if 价格配置 and 字段名 in 价格配置 and 价格配置[字段名] is not None:
                price = float(价格配置[字段名])
            else:
                price = 默认价格配置[卡片类型]

            prices.append({
                'type': 卡片类型,
                'price': price
            })

        # 构建响应数据
        response_data = {
            'success': True,
            'message': '价格查询成功',
            'data': {
                'prices': prices,
                'currency': 'CNY',
                'update_time': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
        }

        logging.info("价格查询请求处理成功")
        return jsonify(response_data)

    except Exception as e:
        logging.error(f"价格查询API错误: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'message': f'查询价格失败: {str(e)}'
        }), 500


@app.after_request
def log_response_info(response):
    """在每个请求处理后记录响应信息。"""
    logging.info(f"响应状态: {response.status_code} - {request.method} {request.full_path}")
    return response

if __name__ == '__main__':
    try:
        # 设置服务器端口
        port = int(os.environ.get('PAID_PORT', 5270))

        # 启动服务器
        logging.info(f"付费限制服务启动在端口 {port}...")
        app.run(host='0.0.0.0', port=port, debug=False)
    except Exception as e:
        logging.critical(f"付费服务启动失败: {e}", exc_info=True)
