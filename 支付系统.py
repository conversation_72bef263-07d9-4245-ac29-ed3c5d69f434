#服务端 避免冗余
import random
import datetime
import logging
import string
import time
import threading
import mysql.connector
from typing import Optional
from alipay.aop.api.AlipayClientConfig import AlipayClientConfig
from alipay.aop.api.DefaultAlipayClient import DefaultAlipayClient
from alipay.aop.api.request.AlipayTradePagePayRequest import AlipayTradePagePayRequest
from alipay.aop.api.util.SignatureUtils import verify_with_rsa
from flask import request, jsonify

# 配置日志轮转
from logging.handlers import RotatingFileHandler

# 创建日志格式
log_formatter = logging.Formatter('%(asctime)s - %(levelname)s - [%(threadName)s] - %(filename)s:%(lineno)d - %(message)s')

# 创建自定义过滤器，只允许特定关键日志通过
class 关键日志过滤器(logging.Filter):
    def filter(self, record):
        # 警告、错误和严重错误级别始终通过
        if record.levelno >= logging.WARNING:
            return True

        # 特定的关键信息性日志也允许通过
        if record.levelno == logging.INFO:
            关键信息 = [
                "支付成功", "续卡成功", "支付失败", "验证失败", "服务启动",
                "创建支付", "订单处理", "卡密生成", "支付通知", "订单查询",
                "代理统计", "数据库", "连接失败", "事务", "轮转", "IP拉黑", "异常访问"
            ]

            # 如果日志消息包含任何关键词，允许通过
            for 关键词 in 关键信息:
                if 关键词 in record.getMessage():
                    return True

            # 其他INFO级别日志不通过
            return False

        # 其他级别（如DEBUG）默认不通过
        return False

# 创建控制台处理器
console_handler = logging.StreamHandler()
console_handler.setFormatter(log_formatter)

# 创建文件处理器（带轮转功能）
# maxBytes=10485760 表示日志文件达到10MB时轮转
# backupCount=5 表示保留5个备份文件
file_handler = RotatingFileHandler(
    filename='支付系统.log',
    mode='a',  # 追加模式
    maxBytes=10485760,  # 10MB
    backupCount=5,
    encoding='utf-8'
)
file_handler.setFormatter(log_formatter)

# 配置根日志记录器
root_logger = logging.getLogger()
root_logger.setLevel(logging.INFO)  # 保持INFO级别，但通过过滤器控制
root_logger.addHandler(console_handler)
root_logger.addHandler(file_handler)

# 添加自定义过滤器
root_logger.addFilter(关键日志过滤器())

# IP访问管理类
class IP访问管理器:
    """IP访问频率检查和拉黑管理"""
    
    def __init__(self):
        self.同秒访问记录 = {}  # {IP: {秒: 次数}}
        self.永久拉黑列表 = set()  # 永久拉黑的IP集合
        self.锁 = threading.RLock()
        self.启动清理线程()
        logging.info("IP访问管理器初始化完成")
    
    def 检查IP访问频率(self, IP地址):
        """
        检查IP访问频率，如果同一秒内访问5次或更多则永久拉黑
        
        参数:
            IP地址: 客户端IP地址
            
        返回:
            (是否允许访问, 错误消息)
        """
        try:
            with self.锁:
                # 检查是否已被永久拉黑
                if IP地址 in self.永久拉黑列表:
                    logging.warning(f"已拉黑IP尝试访问: {IP地址}")
                    return False, "访问被拒绝，请稍后重试"
                
                # 获取当前时间（精确到秒）
                当前时间 = datetime.datetime.now()
                当前秒 = 当前时间.strftime('%Y-%m-%d %H:%M:%S')
                
                # 初始化IP访问记录
                if IP地址 not in self.同秒访问记录:
                    self.同秒访问记录[IP地址] = {}
                
                # 获取当前秒的访问次数
                访问次数 = self.同秒访问记录[IP地址].get(当前秒, 0)
                访问次数 += 1
                
                # 更新访问次数
                self.同秒访问记录[IP地址][当前秒] = 访问次数
                
                # 检查是否超过阈值（5次）
                if 访问次数 >= 5:
                    # 永久拉黑IP
                    self.永久拉黑列表.add(IP地址)
                    logging.error(f"IP {IP地址} 因同秒访问{访问次数}次被永久拉黑")
                    return False, "访问被拒绝，请稍后重试"
                
                return True, "访问检查通过"
                
        except Exception as e:
            logging.error(f"IP访问频率检查失败: {e}", exc_info=True)
            return True, "访问检查通过"  # 出错时不阻止正常使用
    
    def 清理过期记录(self):
        """清理过期的访问记录（保留最近5分钟的记录）"""
        try:
            with self.锁:
                当前时间 = datetime.datetime.now()
                过期时间 = 当前时间 - datetime.timedelta(minutes=5)
                
                清理数量 = 0
                for IP地址 in list(self.同秒访问记录.keys()):
                    IP记录 = self.同秒访问记录[IP地址]
                    过期秒数 = []
                    
                    for 秒字符串 in IP记录.keys():
                        try:
                            记录时间 = datetime.datetime.strptime(秒字符串, '%Y-%m-%d %H:%M:%S')
                            if 记录时间 < 过期时间:
                                过期秒数.append(秒字符串)
                        except:
                            过期秒数.append(秒字符串)  # 格式错误的也清理掉
                    
                    # 删除过期记录
                    for 过期秒 in 过期秒数:
                        del IP记录[过期秒]
                        清理数量 += 1
                    
                    # 如果IP没有任何记录了，删除整个IP条目
                    if not IP记录:
                        del self.同秒访问记录[IP地址]
                
                if 清理数量 > 0:
                    logging.debug(f"IP访问记录清理完成，清理了{清理数量}条过期记录")
                    
        except Exception as e:
            logging.error(f"IP访问记录清理失败: {e}", exc_info=True)
    
    def 启动清理线程(self, 间隔=60):
        """启动定期清理线程"""
        def 清理任务():
            while True:
                time.sleep(间隔)
                try:
                    self.清理过期记录()
                except Exception as e:
                    logging.error(f"IP访问记录清理任务出错: {e}", exc_info=True)
        
        清理线程 = threading.Thread(target=清理任务, name="IPCleanupThread_Payment", daemon=True)
        清理线程.start()
        logging.info(f"IP访问记录清理任务已启动，清理间隔: {间隔}秒")
    
    def 获取拉黑统计(self):
        """获取拉黑统计信息"""
        with self.锁:
            return {
                "永久拉黑数量": len(self.永久拉黑列表),
                "当前监控IP数量": len(self.同秒访问记录)
            }

# 创建全局IP访问管理器实例
IP管理器 = IP访问管理器()

class Config:
    """系统配置类"""
    # 支付宝配置
    支付宝配置 = {
        'app_id': "2021004138616033",
        'private_key': """MIIEowIBAAKCAQEAm24Mf8kDydF4YlGu8X3IZ/l+4WGsuXvUeug/i64NctXr9eqEoEmU6oLuWXe20RS8A6O/S/4/AVPD4zBWGExBwuI/+Q3/8jzNoXEVDr8mkUXB5/o1PXYjaBYjUw1pvqWCln203VEajBFG6cqXIN4DyYyosciyhylzHH6jWMhVBWPL1bnDT5o5nyzHz0lh+kKKZHwuyz9Mtp49mbtFYO34oSUQc9Eru/SigRx9MkcXKUPh2tvSvkcJ8UaKpH0/0ek9+51V69CyTGwRmyj8j3wL+Puw9whilf27xM/1+Jdko7C6SpecuZYNUsX9qrIOv/UWDPZ+jQGE1bOnM8ODbHl8VwIDAQABAoIBAHHsg7TV/bTbmEVYYcYXz+CF+v3vvc5meSNtP8Z92NWm/sDNGFD0HfaV6WYiXRl6/SQdzYVB4aRzZU0MjM5adokFlKZUIJnI/1+ppymb6gTpAGur9YILKYCNWW7ykeNvJPxxPzRI0hCASV3rZEJu+I+uTaeh11lChGEKYWgdzlOX37coe6CetCtTBQja/KtllJWf7cWJdrBQe1CzQRydoOEoPfdu9yxOQGHfURRcjuiR4vXM3iYGImMhwZmV0ytRYIdImosmcm/QSOX2kTDmYghT+OCqZwoIfSHeW+sHGSV7R0btamVFyJXhT3QDUsrbLily9D0jXe6apF3nY8CaoUECgYEA4Nt93W6se+AgfnruTqPktPFOoWQBK3NmtZ5rLNt5PBwZfQIXD5lqlk1AOqsd8t+Ta9I5TiszcSUGh8uFlKJzDvFej2QrCFl8a0eZmQfLXh2a81HtfySpJgHFR3Lypc+Zok7K+nW9OKVO1RB14ptj3hgXSzldyL56uhwV0TfFwi8CgYEAsPTyO1ALJ6lPAgCZjG2ycUM7hLglZo5Hmz8LszjTrQdJyN4DnpnYR1YTipUSfDy5X80pDUnfDCOmgELPpup0We1PwXUo4o5ITgSaf+gBEpEj5Qk3ElE1Ku2YBwurwgXioK4Slx9//dJl+D9oiko5eJDorX71YpcJwZ+e2bT6JlkCgYBVzy1/PWcRdvyvh9Y9qIn1C0mIiZNws390u9oGqlLfv5FyhUOrrk5gwWZLfVXWg7/Mm9NGo3HkMc8GL4Lwr/Xcs/v0GLPbYdr7Tb2uCq1vYzjotlE9/g2W9YIFvDYhVNI6gHsxjZECdwNYiAmUytwXu3XYPTIvE5HeSC2DRBdx/QKBgQCFtN8e3/E9y5kuKyPaTrzJBgL/2mAwKIuEi1Oc3PBcSih/RPm5KgeYhzqGuPLZusxazdsZ9lo5gup4+YKLXZYCqORdidgVr2qEwMe4o6XD0LUthxP8nC2vPQ1lLXVfSjEeNclINP3x3Ls9ozOrYkAgkGWvSkHBd9xgV+qgFzX4KQKBgHEXLnaKO7dF0+KDOJzpgj4aW2Az/+ixFb/8DPOPSz0AGyvB18da97OlpUEQum8/eCFThXHc46/Gyo8ew9KgSfJYnFpxJUE5z4d592+Gi9fuSKSBRdmv6reXENHb32G+yjkGZzfm802UBQbMtOE0u6RUGFlEdVFO7105gpSNdSsK""",
        'alipay_public_key': """MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAkPqSGRasiee10yKOG8pyCBRvBcjAIPmS4A4fbSYtyFUEv30VNzIbQymlZf70T+uIY6qQsscMdZbEnmbiVyWBvO42641DVCmvN+1brdnV0yhgtFPBYELgddvxPvcfIMeeeStDEzfTjuGHYFeMmChcjki7aA44B3ooN+LKUH4NkBXD7fxEGcEueZssiy90ZlN/6x0K6YYLMOtUcpcyVsqCHa8k9N/1Jdr49AldK8BBYkc3x9kz0bw11tgKe1aJG1Q+TqLoGv+SstPcOh2CBM2M4t153TNAupJXUg4U/UvkGlla5DOHTPnJzRht8hkSYsdaKLdJ8+00sfxQcqpM7U5q0wIDAQAB""",
        'app_name': "卡密验证系统",
        'return_url': "http://**************:5001/payment_result", # 支付宝回调地址
        'notify_url': "http://**************:5001/alipay_notify" # 支付宝异步通知地址
    }

    # 卡密类型配置
    卡密类型 = {
        '有效类型': ['时卡', '天卡', '周卡', '月卡', '季卡', '年卡'],
        '类型价格': {
            '时卡': 2.0,
            '天卡': 6.6,
            '周卡': 9.9,
            '月卡': 29.9,
            '季卡': 89.9,
            '年卡': 360.0
        }
    }

    # 数据库配置
    数据库配置 = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': 'Yuyu6709.',
        'database': 'kami3162'
    }

    # 服务配置
    服务配置 = {
        'host': '0.0.0.0',     # 绑定的IP地址，0.0.0.0表示所有网卡
        'port': 5001,          # 服务端口
        'debug': False,         # 是否启用调试模式
        'public_host': '**************',  # 公网IP或域名
        'public_port': 5001    # 公网端口
    }

    @classmethod
    def get_public_url(cls):
        """获取公网URL，用于支付回调等场景"""
        return f"http://{cls.服务配置['public_host']}:{cls.服务配置['public_port']}"

class 数据库管理器:
    """数据库连接和操作管理"""
    def __init__(self, 配置=None):
        self.配置 = 配置 or Config.数据库配置

    def 获取连接(self):
        """获取数据库连接"""
        return mysql.connector.connect(**self.配置)

    def 执行查询(self, sql, 参数=None):
        """执行查询并返回结果"""
        try:
            conn = self.获取连接()
            cursor = conn.cursor(dictionary=True)
            cursor.execute(sql, 参数 or ())
            result = cursor.fetchall()
            cursor.close()
            conn.close()
            return result
        except Exception as e:
            logging.error(f"查询执行失败: {e}")
            raise

    def 执行单条查询(self, sql, 参数=None):
        """执行查询并返回单条结果"""
        try:
            conn = self.获取连接()
            cursor = conn.cursor(dictionary=True)
            cursor.execute(sql, 参数 or ())
            result = cursor.fetchone()
            cursor.close()
            conn.close()
            return result
        except Exception as e:
            logging.error(f"单条查询执行失败: {e}")
            raise

    def 执行更新(self, sql, 参数=None):
        """执行更新操作"""
        try:
            conn = self.获取连接()
            cursor = conn.cursor()
            cursor.execute(sql, 参数 or ())
            conn.commit()
            affected_rows = cursor.rowcount
            cursor.close()
            conn.close()
            return affected_rows
        except Exception as e:
            logging.error(f"更新执行失败: {e}")
            raise

    def 在事务中执行(self, 回调函数):
        """
        在事务中执行一系列数据库操作

        参数:
            回调函数: 接收连接和游标的函数，在其中执行SQL操作

        返回:
            回调函数的返回值
        """
        conn = None
        try:
            conn = self.获取连接()
            # 使用正确的方式设置autocommit - 对于mysql.connector，使用start_transaction()
            conn.start_transaction()
            cursor = conn.cursor(dictionary=True)

            # 执行回调函数，传入连接和游标
            result = 回调函数(conn, cursor)

            # 提交事务
            conn.commit()
            return result
        except Exception as e:
            # 发生异常，回滚事务
            if conn:
                conn.rollback()
            logging.error(f"事务执行失败: {e}")
            raise
        finally:
            # 确保关闭游标和连接
            if conn:
                if conn.is_connected():
                    cursor.close()
                    conn.close()

class 卡密管理器:
    """卡密管理类，提供卡密相关功能"""
    def __init__(self, 数据库=None):
        self.数据库 = 数据库 or 数据库管理器()
        self.卡密类型配置 = Config.卡密类型

    def _计算到期时间(self, 卡密类型, 当前时间=None):
        """
        根据卡密类型计算到期时间

        参数:
            卡密类型: 卡密类型（时卡、天卡、周卡、月卡、年卡）
            当前时间: 当前时间，默认为None，将使用当前系统时间

        返回:
            到期时间对象和格式化的到期时间字符串
        """
        当前时间 = 当前时间 or datetime.datetime.now()

        到期时间映射 = {
            "时卡": datetime.timedelta(hours=1),
            "天卡": datetime.timedelta(days=1),
            "周卡": datetime.timedelta(days=7),
            "月卡": datetime.timedelta(days=30),
            "年卡": datetime.timedelta(days=365)
        }

        到期时间 = 当前时间 + 到期时间映射.get(卡密类型, datetime.timedelta(days=1))
        到期时间字符串 = 到期时间.strftime('%Y-%m-%d %H:%M:%S')

        return 到期时间, 到期时间字符串

    def _生成随机卡密(self, 长度=10):
        """
        生成随机卡密

        参数:
            长度: 卡密长度，默认10位

        返回:
            生成的卡密
        """
        字符集 = string.ascii_letters + string.digits
        卡密 = ''.join(random.choice(字符集) for _ in range(长度))
        return 卡密

    def _验证卡密字符串(self, 卡密):
        """
        验证卡密字符串格式是否有效

        参数:
            卡密: 要验证的卡密字符串

        返回:
            bool: 卡密格式是否有效
        """
        # 检查基本长度要求
        if not 卡密 or len(卡密) < 4:
            return False

        # 直接检查是否全部是字母和数字
        return all(c in string.ascii_letters + string.digits for c in 卡密)

    def 查询卡密状态(self, 卡密):
        """
        查询卡密状态

        参数:
            卡密: 要查询的卡密

        返回:
            (成功状态, 消息, 卡密数据)
        """
        try:
            # 查询卡密信息
            查询SQL = """
                SELECT 卡密, 类型, 机器码, 首次登录时间, 最近登录, 到期时间, 本月登录次数, 最大数量, 备注, 剩余额度
                FROM 卡密系统
                WHERE 卡密 = %s
                LIMIT 1
            """
            结果 = self.数据库.执行单条查询(查询SQL, [卡密])

            if not 结果:
                return False, "卡密不存在", None

            return True, "查询成功", 结果

        except Exception as e:
            logging.error(f"查询卡密状态失败: {e}")
            return False, f"查询失败: {str(e)}", None

class 支付宝支付系统:
    """支付宝支付系统类"""
    def __init__(self, 配置=None, 数据库=None):
        self.配置 = 配置 or Config.支付宝配置
        self.app_name = self.配置.get('app_name')
        self.卡密类型配置 = Config.卡密类型
        self.数据库 = 数据库 or 数据库管理器()

        # 初始化支付宝客户端配置
        self._config = AlipayClientConfig()
        self._config.app_id = self.配置.get('app_id')
        self._config.app_private_key = self.配置.get('private_key')
        self._config.alipay_public_key = self.配置.get('alipay_public_key')
        self._config.sign_type = "RSA2"
        self._config.charset = "utf-8"

        self._client = DefaultAlipayClient(alipay_client_config=self._config)

    def 获取卡片价格(self, 卡密类型):
        """
        从数据库获取卡片价格，如果数据库中没有则使用默认价格

        参数:
            卡密类型: 卡片类型

        返回:
            价格（float）
        """
        try:
            # 从cursorpro表查询价格信息
            # 注意：支付系统没有缓存机制，每次都直接查询数据库获取最新价格
            价格配置 = self.数据库.执行单条查询(
                "SELECT 时卡, 天卡, 周卡, 月卡, 季卡, 年卡 FROM cursorpro ORDER BY id DESC LIMIT 1"
            )

            # 字段名映射
            字段映射 = {
                '时卡': '时卡',
                '天卡': '天卡',
                '周卡': '周卡',
                '月卡': '月卡',
                '季卡': '季卡',
                '年卡': '年卡'
            }

            # 优先使用数据库中的价格
            if 价格配置 and 卡密类型 in 字段映射:
                字段名 = 字段映射[卡密类型]
                if 字段名 in 价格配置 and 价格配置[字段名] is not None:
                    return float(价格配置[字段名])

            # 如果数据库中没有，使用默认价格
            默认价格 = self.卡密类型配置['类型价格'].get(卡密类型)
            if 默认价格 is not None:
                return float(默认价格)

            # 如果都没有，返回0
            logging.warning(f"未找到卡密类型 {卡密类型} 的价格配置")
            return 0.0

        except Exception as e:
            logging.error(f"获取卡片价格失败: {e}")
            # 出错时使用默认价格
            默认价格 = self.卡密类型配置['类型价格'].get(卡密类型, 0.0)
            return float(默认价格)

    def 创建支付页面(self, 订单号: str, 总金额: float, 商品名称: Optional[str] = None, 机器码: Optional[str] = None, 程序名: Optional[str] = None, 卡密类型: Optional[str] = None, 备注: Optional[str] = None, 卡密: Optional[str] = None, 是否续卡: bool = False) -> str:
        """
        创建支付页面

        参数:
            订单号: 订单号
            总金额: 支付金额
            商品名称: 商品名称，默认为app_name
            机器码: 机器码（可选，用于回传）
            程序名: 程序名（可选，用于回传）
            卡密类型: 卡密类型（可选，用于回传）
            备注: 备注信息（可选，用于回传）
            卡密: 卡密（续卡时需要，用于回传）
            是否续卡: 是否为续卡订单

        返回:
            支付宝支付链接
        """
        try:
            商品名称 = 商品名称 or self.app_name or "卡密验证系统"

            request = AlipayTradePagePayRequest()
            # 设置同步跳转地址
            request.return_url = self.配置.get('return_url')
            # 设置异步通知地址
            request.notify_url = self.配置.get('notify_url')

            # 创建业务参数字典
            biz_content = {
                "out_trade_no": 订单号,
                "total_amount": f"{总金额:.2f}",
                "subject": 商品名称,
                "product_code": "FAST_INSTANT_TRADE_PAY",
                "qr_pay_mode": "2"
            }

            # 添加回传参数
            passback_params = ""
            if 机器码:
                passback_params = f"machine_code={机器码}"
            if 程序名:
                if passback_params:
                    passback_params += f"&app_name={程序名}"
                else:
                    passback_params = f"app_name={程序名}"

            # 添加卡密类型到回传参数中
            if 卡密类型:
                if passback_params:
                    passback_params += f"&card_type={卡密类型}"
                else:
                    passback_params = f"card_type={卡密类型}"

            # 添加备注到回传参数中
            if 备注:
                if passback_params:
                    passback_params += f"&remark={备注}"
                else:
                    passback_params = f"remark={备注}"

            # 添加卡密到回传参数中（续卡时需要）
            if 卡密:
                if passback_params:
                    passback_params += f"&card_key={卡密}"
                else:
                    passback_params = f"card_key={卡密}"

            # 添加是否续卡标志
            if 是否续卡:
                if passback_params:
                    passback_params += f"&is_renewal=1"
                else:
                    passback_params = f"is_renewal=1"

            if passback_params:
                import urllib.parse
                biz_content["passback_params"] = urllib.parse.quote(passback_params)

            # 设置支付业务参数
            request.biz_content = biz_content

            response = self._client.sdk_execute(request)
            if not response:
                return ""

            # 使用支付宝开放平台上设置的网关
            full_url = f"https://openapi.alipay.com/gateway.do?{response}"

            return full_url
        except Exception as e:
            logging.error(f"创建支付时出错: {e}")
            return ""

    def 验证支付宝通知(self, 数据):
        """验证支付宝异步通知的真实性"""
        try:
            # 创建数据副本，避免修改原始数据
            数据副本 = 数据.copy()

            # 获取签名
            签名 = 数据副本.pop('sign', None)
            # 获取签名类型
            签名类型 = 数据副本.pop('sign_type', None)

            if not 签名 or 签名类型 != 'RSA2':
                return False

            # 按照支付宝要求，将参数排序并拼接成字符串
            ordered_items = sorted(数据副本.items())
            message = '&'.join(f"{k}={v}" for k, v in ordered_items)

            # 使用支付宝公钥验证签名
            result = verify_with_rsa(self.配置.get('alipay_public_key'), message.encode('utf-8'), 签名)
            return result
        except Exception as e:
            logging.error(f"验证签名出错: {e}")
            return False

    def 生成支付链接(self, 程序名: Optional[str] = None, 卡密类型: Optional[str] = None, 机器码: Optional[str] = None, 备注: str = "官方版") -> dict:
        """
        根据程序名、卡密类型和机器码生成支付链接

        参数:
            程序名: 程序名称
            卡密类型: 卡密类型，必须是有效的类型（时卡、天卡、周卡、月卡、年卡）
            机器码: 机器码（可选）
            备注: 备注信息（可选）

        返回:
            dict: 包含成功状态、订单号、支付链接、金额和卡密类型的字典
        """
        try:
            # 验证卡密类型是否有效
            有效类型 = self.卡密类型配置['有效类型']
            if 卡密类型 not in 有效类型:
                return {"success": False, "message": f"无效的卡密类型，有效类型: {', '.join(有效类型)}"}

            # 从数据库获取金额
            金额 = self.获取卡片价格(卡密类型)
            if 金额 <= 0:
                return {"success": False, "message": f"卡密类型'{卡密类型}'没有设置价格"}

            # 生成订单号（直接内联生成，不再使用单独的函数）
            timestamp = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
            random_num = str(random.randint(100, 999))
            订单号 = f"{timestamp}{random_num}"

            # 设置商品名称，简化为只显示卡密类型
            商品名称 = 卡密类型

            # 创建支付链接
            支付链接 = self.创建支付页面(订单号, 金额, 商品名称, 机器码, 程序名, 卡密类型, 备注)

            if not 支付链接:
                return {"success": False, "message": "创建支付链接失败"}

            return {
                "success": True,
                "order_no": 订单号,
                "payment_url": 支付链接,
                "amount": 金额,
                "card_type": 卡密类型,
                "remark": 备注
            }
        except Exception as e:
            logging.error(f"生成支付链接时出错: {e}")
            return {"success": False, "message": f"生成支付链接时出错: {str(e)}"}

    def 生成续卡支付链接(self, 卡密: str, 卡密类型: str, 程序名: Optional[str] = None, 机器码: Optional[str] = None, 备注: str = "官方版") -> dict:
        """
        根据卡密和卡密类型生成续卡支付链接

        参数:
            卡密: 要续费的卡密
            卡密类型: 续费的卡密类型，必须是有效的类型（时卡、天卡、周卡、月卡、年卡）
            程序名: 程序名称（可选）
            机器码: 机器码（可选）
            备注: 备注信息（可选）

        返回:
            dict: 包含成功状态、订单号、支付链接、金额和卡密类型的字典
        """
        try:
            # 验证卡密类型是否有效
            有效类型 = self.卡密类型配置['有效类型']
            if 卡密类型 not in 有效类型:
                return {"success": False, "message": f"无效的卡密类型，有效类型: {', '.join(有效类型)}"}

            # 从数据库获取金额
            金额 = self.获取卡片价格(卡密类型)
            if 金额 <= 0:
                return {"success": False, "message": f"卡密类型'{卡密类型}'没有设置价格"}

            # 生成订单号
            timestamp = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
            random_num = str(random.randint(100, 999))
            订单号 = f"XK{timestamp}{random_num}"  # 续卡订单号添加XK前缀

            # 设置商品名称，添加续卡标识
            商品名称 = f"续卡-{卡密类型}"

            # 创建支付链接
            支付链接 = self.创建支付页面(订单号, 金额, 商品名称, 机器码, 程序名, 卡密类型, 备注, 卡密, True)

            if not 支付链接:
                return {"success": False, "message": "创建续卡支付链接失败"}

            return {
                "success": True,
                "order_no": 订单号,
                "payment_url": 支付链接,
                "amount": 金额,
                "card_type": 卡密类型,
                "card_key": 卡密,
                "remark": 备注
            }
        except Exception as e:
            logging.error(f"生成续卡支付链接时出错: {e}")
            return {"success": False, "message": f"生成续卡支付链接时出错: {str(e)}"}

# 添加支付相关路由处理函数
class 支付API处理:
    """支付API相关处理函数"""
    def __init__(self, 支付系统=None, 数据库=None):
        self.数据库 = 数据库 or 数据库管理器()
        self.支付系统 = 支付系统 or 支付宝支付系统(数据库=self.数据库)
        self.卡密管理 = 卡密管理器(self.数据库)

    # 创建支付链接API端点
    def create_payment(self):
        """创建支付链接API端点"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({
                    'success': False,
                    'message': '缺少请求数据'
                }), 400

            # 验证必填参数
            if 'card_type' not in data:
                return jsonify({
                    'success': False,
                    'message': '缺少卡密类型参数'
                }), 400

            # 获取参数
            卡密类型 = data.get('card_type')
            机器码 = data.get('machine_code')
            程序名 = data.get('app_name')
            备注 = data.get('remark', '官方版')

            # 检查IP访问频率
            IP地址 = request.remote_addr
            访问检查结果, 错误消息 = IP管理器.检查IP访问频率(IP地址)
            if not 访问检查结果:
                return jsonify({
                    'success': False,
                    'message': 错误消息
                }), 429

            # 生成支付链接
            result = self.支付系统.生成支付链接(程序名, 卡密类型, 机器码, 备注)

            return jsonify(result)
        except Exception as e:
            logging.error(f"创建支付链接API错误: {e}")
            return jsonify({
                'success': False,
                'message': f'服务器错误: {str(e)}'
            }), 500

    # 创建续卡支付链接API端点
    def create_renewal_payment(self):
        """创建续卡支付链接API端点"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({
                    'success': False,
                    'message': '缺少请求数据'
                }), 400

            # 验证必填参数
            if 'card_key' not in data:
                return jsonify({
                    'success': False,
                    'message': '缺少卡密参数'
                }), 400

            if 'card_type' not in data:
                return jsonify({
                    'success': False,
                    'message': '缺少卡密类型参数'
                }), 400

            # 获取参数
            卡密 = data.get('card_key')
            卡密类型 = data.get('card_type')
            机器码 = data.get('machine_code')
            程序名 = data.get('app_name')
            备注 = data.get('remark', '官方版')

            # 检查IP访问频率
            IP地址 = request.remote_addr
            访问检查结果, 错误消息 = IP管理器.检查IP访问频率(IP地址)
            if not 访问检查结果:
                return jsonify({
                    'success': False,
                    'message': 错误消息
                }), 429

            # 检查卡密是否存在
            卡密信息 = self.数据库.执行单条查询("SELECT * FROM 卡密系统 WHERE 卡密 = %s", (卡密,))
            if not 卡密信息:
                return jsonify({
                    'success': False,
                    'message': '卡密不存在，无法续卡'
                }), 400

            # 生成续卡支付链接
            result = self.支付系统.生成续卡支付链接(卡密, 卡密类型, 程序名, 机器码, 备注)

            return jsonify(result)
        except Exception as e:
            logging.error(f"创建续卡支付链接API错误: {e}")
            return jsonify({
                'success': False,
                'message': f'服务器错误: {str(e)}'
            }), 500

    # 代理服务器API端点
    def create_proxy_payment(self):
        """创建代理服务器支付链接API端点，只需要卡密参数，固定价格19.9元"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({
                    'success': False,
                    'message': '缺少请求数据'
                }), 400

            # 验证必填参数
            if 'card_key' not in data:
                return jsonify({
                    'success': False,
                    'message': '缺少卡密参数'
                }), 400

            # 获取参数
            卡密 = data.get('card_key')

            # 检查IP访问频率
            IP地址 = request.remote_addr
            访问检查结果, 错误消息 = IP管理器.检查IP访问频率(IP地址)
            if not 访问检查结果:
                return jsonify({
                    'success': False,
                    'message': 错误消息
                }), 429

            # 检查卡密是否存在
            卡密信息 = self.数据库.执行单条查询("SELECT * FROM 卡密系统 WHERE 卡密 = %s", (卡密,))
            if not 卡密信息:
                return jsonify({
                    'success': False,
                    'message': '卡密不存在'
                }), 400

            # 代理服务器固定配置
            卡密类型 = "月卡"
            程序名 = "代理服务器"
            备注 = "代理服务器"
            固定金额 = 19.9  # 代理服务器固定价格19.9元

            # 生成订单号（代理服务器订单号添加DL前缀）
            timestamp = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
            random_num = str(random.randint(100, 999))
            订单号 = f"DL{timestamp}{random_num}"

            # 设置商品名称
            商品名称 = "代理服务器"

            # 创建支付链接
            支付链接 = self.支付系统.创建支付页面(订单号, 固定金额, 商品名称, None, 程序名, 卡密类型, 备注, 卡密, True)

            if not 支付链接:
                return jsonify({
                    'success': False,
                    'message': '创建代理服务器支付链接失败'
                })

            return jsonify({
                "success": True,
                "order_no": 订单号,
                "payment_url": 支付链接,
                "amount": 固定金额,
                "card_type": 卡密类型,
                "card_key": 卡密,
                "remark": 备注
            })
        except Exception as e:
            logging.error(f"创建代理服务器支付链接API错误: {e}")
            return jsonify({
                'success': False,
                'message': f'服务器错误: {str(e)}'
            }), 500

    # 提取代理秘钥处理函数
    def _处理代理统计(self, cursor, 代理秘钥, 当前时间, 当前时间字符串):
        """
        处理代理秘钥统计数据更新

        参数:
            cursor: 数据库游标
            代理秘钥: 代理秘钥
            当前时间: 当前时间对象
            当前时间字符串: 当前时间字符串

        返回:
            成功更新返回True，否则返回False
        """
        # 查询代理秘钥是否存在
        cursor.execute("SELECT * FROM 代理秘钥 WHERE 代理秘钥 = %s FOR UPDATE", (代理秘钥,))
        代理信息 = cursor.fetchone()

        if not 代理信息:
            return False

        # 获取当前日期和月份
        当前日期 = 当前时间.date()
        最近激活时间 = 代理信息.get('最近激活时间')

        # 检查是否需要重置今日激活数量
        今日激活数量 = 代理信息.get('今日激活数量', 0)
        if 最近激活时间 and 最近激活时间.date() < 当前日期:
            今日激活数量 = 0  # 如果是新的一天，重置今日激活数量

        # 检查是否需要重置本月激活数量
        本月激活数量 = 代理信息.get('本月激活数量', 0)
        if 最近激活时间 and 最近激活时间.month != 当前时间.month:
            本月激活数量 = 0  # 如果是新的一月，重置本月激活数量

        # 更新代理秘钥表
        cursor.execute(
            """
            UPDATE 代理秘钥
            SET 最近激活时间 = %s,
                今日激活数量 = %s,
                本月激活数量 = %s,
                激活总数 = 激活总数 + 1
            WHERE 代理秘钥 = %s
            """,
            (
                当前时间字符串,
                今日激活数量 + 1,
                本月激活数量 + 1,
                代理秘钥
            )
        )

        logging.info(f"已更新代理秘钥 {代理秘钥} 的激活统计信息")
        return True

    # 提取代理秘钥查询函数
    def _获取代理名字(self, cursor, 备注):
        """
        根据备注(代理秘钥)获取代理名字

        参数:
            cursor: 数据库游标
            备注: 备注信息，可能包含代理秘钥

        返回:
            (代理名字, 原始备注) 元组
        """
        if not 备注 or 备注 == "官方版":
            return 备注, 备注

        # 以备注为代理秘钥
        代理秘钥 = 备注

        # 查询代理秘钥对应的代理名字
        cursor.execute("SELECT 代理名字 FROM 代理秘钥 WHERE 代理秘钥 = %s", (代理秘钥,))
        代理信息 = cursor.fetchone()

        if 代理信息 and 代理信息.get('代理名字'):
            return 代理信息.get('代理名字'), 代理秘钥  # 返回代理名字和原始秘钥

        return 备注, 备注  # 没找到代理信息，返回原备注

    # 提取订单记录函数
    def _记录订单信息(self, cursor, 订单数据):
        """
        记录订单信息到数据库

        参数:
            cursor: 数据库游标
            订单数据: 包含订单信息的字典
        """
        cursor.execute(
            """
            INSERT INTO 支付订单 (订单号, 交易号, 金额, 机器码, 程序名, 卡密类型, 卡密, 支付时间, 有效期, 代理名字)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """,
            (
                订单数据['订单号'],
                订单数据['交易号'],
                订单数据['金额'],
                订单数据['机器码'] or "",
                订单数据['程序名'] or "",
                订单数据['卡密类型'],
                订单数据['卡密'],
                订单数据['支付时间'],
                订单数据['到期时间'],
                订单数据['代理名字']
            )
        )

    # 支付宝异步通知API端点
    def alipay_notify(self):
        """处理支付宝的支付结果通知"""
        try:
            # 检查IP访问频率（支付宝通知也需要防护）
            IP地址 = request.remote_addr
            访问检查结果, 错误消息 = IP管理器.检查IP访问频率(IP地址)
            if not 访问检查结果:
                logging.warning(f"拉黑IP尝试访问支付宝通知接口: {IP地址}")
                return "fail"

            # 获取通知数据
            数据 = request.form.to_dict()

            # 验证通知的真实性
            if not self.支付系统.验证支付宝通知(数据):
                return "fail"

            # 验证通知类型
            trade_status = 数据.get('trade_status')
            if trade_status != 'TRADE_SUCCESS':
                return "success"  # 返回success，避免支付宝重复通知

            # 获取订单信息
            订单号 = 数据.get('out_trade_no')
            交易号 = 数据.get('trade_no')
            金额 = float(数据.get('total_amount', 0))
            商品名称 = 数据.get('subject', '')

            # 获取回传参数
            回传参数 = 数据.get('passback_params', '')
            机器码 = None
            程序名 = None
            卡密类型 = None
            备注 = "官方版"  # 默认备注
            卡密 = None
            是否续卡 = False

            # 解析回传参数
            if 回传参数:
                import urllib.parse
                回传参数 = urllib.parse.unquote(回传参数)
                参数列表 = 回传参数.split('&')
                for 参数 in 参数列表:
                    if '=' in 参数:
                        键, 值 = 参数.split('=', 1)
                        if 键 == 'machine_code':
                            机器码 = 值
                        elif 键 == 'app_name':
                            程序名 = 值
                        elif 键 == 'card_type':
                            卡密类型 = 值
                        elif 键 == 'remark' or 键 == 'memo':
                            备注 = 值
                        elif 键 == 'card_key':
                            卡密 = 值
                        elif 键 == 'is_renewal' and 值 == '1':
                            是否续卡 = True

            # 如果回传参数中没有卡密类型，使用商品名称作为卡密类型
            if not 卡密类型:
                卡密类型 = 商品名称  # 直接使用商品名称作为卡密类型
                # 如果是续卡订单，商品名称可能是"续卡-XXX"格式，需要处理
                if 卡密类型.startswith("续卡-"):
                    卡密类型 = 卡密类型[3:]  # 移除"续卡-"前缀

            # 使用事务处理订单
            def 处理订单(_conn, cursor):
                # 检查订单是否已经处理过（在事务中，使用行锁避免并发问题）
                cursor.execute("SELECT * FROM 支付订单 WHERE 订单号 = %s FOR UPDATE", (订单号,))
                已存在订单 = cursor.fetchone()

                if 已存在订单:
                    logging.info(f"订单 {订单号} 已经处理过，跳过")
                    return True

                当前时间 = datetime.datetime.now()
                当前时间字符串 = 当前时间.strftime('%Y-%m-%d %H:%M:%S')

                # 使用卡密管理器计算到期时间
                卡密管理器实例 = 卡密管理器()

                # 获取代理名字
                最终备注, 代理秘钥 = self._获取代理名字(cursor, 备注)

                # 如果是续卡，查询卡密信息并延长到期时间
                if 是否续卡 and 卡密:
                    # 查询卡密是否存在
                    cursor.execute("SELECT * FROM 卡密系统 WHERE 卡密 = %s FOR UPDATE", (卡密,))
                    卡密信息 = cursor.fetchone()

                    if not 卡密信息:
                        logging.error(f"续卡失败：卡密 {卡密} 不存在")
                        return False

                    # 获取原到期时间和原剩余额度
                    原到期时间 = 卡密信息.get('到期时间')
                    原剩余额度 = 卡密信息.get('剩余额度', 0) or 0  # 确保不为None

                    # 根据卡密类型设置不同的续费额度 (50额度*30个=1500额度一天，乘以天数)
                    if 卡密类型 == "时卡":
                        新增额度 = 1500    # 时卡续费1500额度 (1天)
                    elif 卡密类型 == "天卡":
                        新增额度 = 1500    # 天卡续费1500额度 (1天)
                    elif 卡密类型 == "周卡":
                        新增额度 = 10500   # 周卡续费10500额度 (7天 * 1500)
                    elif 卡密类型 == "月卡":
                        新增额度 = 45000   # 月卡续费45000额度 (30天 * 1500)
                    elif 卡密类型 == "年卡":
                        新增额度 = 547500  # 年卡续费547500额度 (365天 * 1500)
                    else:
                        新增额度 = 1500    # 其他卡型续费1500额度

                    # 判断卡密是否过期，决定额度叠加方式
                    卡密已过期 = False
                    if 原到期时间:
                        卡密已过期 = 原到期时间 <= 当前时间
                    else:
                        卡密已过期 = True  # 没有到期时间视为已过期

                    # 根据是否过期决定额度计算方式
                    if 卡密已过期:
                        # 已过期：从0开始，只给新增额度
                        剩余额度 = 新增额度
                        额度说明 = "已过期，重新分配"
                    else:
                        # 未过期：叠加原有剩余额度
                        剩余额度 = 原剩余额度 + 新增额度
                        额度说明 = "未过期，叠加原额度"

                    # 计算新的到期时间
                    if 原到期时间:
                        # 如果原到期时间大于当前时间，在原基础上延长
                        if 原到期时间 > 当前时间:
                            _, 到期时间字符串 = 卡密管理器实例._计算到期时间(卡密类型, 原到期时间)
                        else:
                            # 如果原到期时间已过，从当前时间开始计算
                            _, 到期时间字符串 = 卡密管理器实例._计算到期时间(卡密类型, 当前时间)
                    else:
                        # 如果没有原到期时间，从当前时间开始计算
                        _, 到期时间字符串 = 卡密管理器实例._计算到期时间(卡密类型, 当前时间)

                    # 检查是否为代理服务器支付，如果是则同时更新代理服务器到期时间
                    if 程序名 == "代理服务器":
                        # 代理服务器支付，同时更新代理服务器到期时间字段
                        cursor.execute(
                            "UPDATE 卡密系统 SET 到期时间 = %s, 剩余额度 = %s, 代理服务器到期时间 = %s WHERE 卡密 = %s",
                            (到期时间字符串, 剩余额度, 到期时间字符串, 卡密)
                        )
                        logging.info(f"代理服务器支付成功，已更新代理服务器到期时间: {到期时间字符串}")
                    else:
                        # 普通续卡，只更新到期时间和剩余额度
                        cursor.execute(
                            "UPDATE 卡密系统 SET 到期时间 = %s, 剩余额度 = %s WHERE 卡密 = %s",
                            (到期时间字符串, 剩余额度, 卡密)
                        )

                    # 记录续卡订单信息
                    订单数据 = {
                        '订单号': 订单号,
                        '交易号': 交易号,
                        '金额': 金额,
                        '机器码': 机器码,
                        '程序名': 程序名,
                        '卡密类型': f"续卡-{卡密类型}",
                        '卡密': 卡密,
                        '支付时间': 当前时间字符串,
                        '到期时间': 到期时间字符串,
                        '代理名字': 最终备注
                    }
                    self._记录订单信息(cursor, 订单数据)

                    # 更新代理秘钥表（如果备注中包含代理秘钥）- 续卡也要计数
                    if 代理秘钥 != 备注 or (备注 and 备注 != "官方版"):
                        self._处理代理统计(cursor, 代理秘钥 or 备注, 当前时间, 当前时间字符串)

                    logging.info(f"续卡成功，订单号: {订单号}, 交易号: {交易号}, 金额: {金额}, 卡密: {卡密}, 原额度: {原剩余额度}, 新增额度: {新增额度}, 总额度: {剩余额度}, 状态: {额度说明}")
                    return True

                # 非续卡订单，执行原有的创建新卡密逻辑
                _, 到期时间字符串 = 卡密管理器实例._计算到期时间(卡密类型, 当前时间)

                # 根据卡密类型设置不同的剩余额度 (50额度*30个=1500额度一天，乘以天数)
                if 卡密类型 == "时卡":
                    剩余额度 = 1500    # 时卡1500额度 (1天)
                elif 卡密类型 == "天卡":
                    剩余额度 = 1500    # 天卡1500额度 (1天)
                elif 卡密类型 == "周卡":
                    剩余额度 = 10500   # 周卡10500额度 (7天 * 1500)
                elif 卡密类型 == "月卡":
                    剩余额度 = 45000   # 月卡45000额度 (30天 * 1500)
                elif 卡密类型 == "年卡":
                    剩余额度 = 547500  # 年卡547500额度 (365天 * 1500)
                else:
                    剩余额度 = 1500    # 其他卡型默认1500额度

                # 使用卡密管理器生成带前缀的随机卡密
                最终卡密 = None

                # 尝试最多10次生成唯一卡密
                for _ in range(10):
                    # 生成随机卡密
                    新卡密 = 卡密管理器实例._生成随机卡密(10)

                    # 检查卡密是否已存在
                    cursor.execute("SELECT 卡密 FROM 卡密系统 WHERE 卡密 = %s", (新卡密,))
                    if cursor.fetchone():
                        continue  # 卡密已存在，重新生成

                    最终卡密 = 新卡密
                    break

                if not 最终卡密:
                    logging.error(f"为订单 {订单号} 生成卡密失败，已尝试10次")
                    return False

                logging.info(f"为订单 {订单号} 生成新卡密: {最终卡密}")

                # 记录订单信息和生成的卡密
                订单数据 = {
                    '订单号': 订单号,
                    '交易号': 交易号,
                    '金额': 金额,
                    '机器码': 机器码,
                    '程序名': 程序名,
                    '卡密类型': 卡密类型,
                    '卡密': 最终卡密,
                    '支付时间': 当前时间字符串,
                    '到期时间': 到期时间字符串,
                    '代理名字': 最终备注
                }
                self._记录订单信息(cursor, 订单数据)

                # 将新卡密插入到卡密系统表
                cursor.execute(
                    """
                    INSERT INTO 卡密系统 (卡密, 类型, 机器码, 首次登录时间, 到期时间, 备注, 剩余额度)
                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                    """,
                    (最终卡密, 卡密类型, 机器码 or "", 当前时间字符串, 到期时间字符串, 最终备注, 剩余额度)
                )

                # 更新代理秘钥表（如果备注中包含代理秘钥）
                if 代理秘钥 != 备注 or (备注 and 备注 != "官方版"):
                    self._处理代理统计(cursor, 代理秘钥 or 备注, 当前时间, 当前时间字符串)

                logging.info(f"支付成功，订单号: {订单号}, 交易号: {交易号}, 金额: {金额}, 卡密: {最终卡密}")
                return True

            # 使用事务执行数据库操作
            self.数据库.在事务中执行(处理订单)

            return "success"  # 返回success告诉支付宝不要再次通知
        except Exception as e:
            logging.error(f"支付宝通知处理错误: {e}")
            return "fail"

    # 查询订单API端点
    def query_order(self):
        """查询订单API端点"""
        try:
            # 检查IP访问频率
            IP地址 = request.remote_addr
            访问检查结果, 错误消息 = IP管理器.检查IP访问频率(IP地址)
            if not 访问检查结果:
                return jsonify({
                    'success': False,
                    'message': 错误消息
                }), 429

            data = request.get_json()
            if not data or 'order_no' not in data:
                return jsonify({
                    'success': False,
                    'message': '缺少订单号参数'
                }), 400

            订单号 = data.get('order_no')

            # 直接从数据库查询订单信息
            订单信息 = self.数据库.执行单条查询(
                """
                SELECT 订单号, 交易号, 金额, 机器码, 程序名, 卡密类型, 卡密, 支付时间, 有效期
                FROM 支付订单 WHERE 订单号 = %s
                """,
                (订单号,)
            )

            if not 订单信息:
                return jsonify({
                    'success': False,
                    'message': '未找到订单信息或订单尚未支付'
                })

            return jsonify({
                'success': True,
                'message': '查询成功',
                'order_info': 订单信息
            })
        except Exception as e:
            logging.error(f"查询订单API错误: {e}")
            return jsonify({
                'success': False,
                'message': f'服务器错误: {str(e)}'
            }), 500

    # 支付结果页面
    def payment_result(self):
        """处理支付宝同步回调，显示支付结果页面"""
        try:
            # 获取订单号
            订单号 = request.args.get('out_trade_no')
            if not 订单号:
                return self._生成支付结果页面("查询失败", "缺少订单号参数", None)

            # 从数据库查询订单和卡密信息
            订单信息 = self.数据库.执行单条查询(
                """
                SELECT 订单号, 交易号, 金额, 机器码, 程序名, 卡密类型, 卡密, 支付时间, 有效期
                FROM 支付订单 WHERE 订单号 = %s
                """,
                (订单号,)
            )

            if not 订单信息:
                return self._生成支付结果页面(
                    "订单查询",
                    "未找到订单信息，订单可能尚未支付成功或正在处理中",
                    {"订单号": 订单号, "状态": "处理中"}
                )

            return self._生成支付结果页面(
                "支付成功",
                "您的订单已处理完成",
                订单信息
            )

        except Exception as e:
            logging.error(f"支付结果页面生成错误: {e}")
            return self._生成支付结果页面("系统错误", f"系统错误: {str(e)}", None)

    def _生成支付结果页面(self, 标题, 消息, 订单信息):
        """生成支付结果HTML页面"""
        html = f"""
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>{标题} - 卡密验证系统</title>
            <style>
                * {{
                    margin: 0;
                    padding: 0;
                    box-sizing: border-box;
                }}
                body {{
                    font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
                    line-height: 1.6;
                    min-height: 100vh;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    padding: 20px;
                }}
                .container {{
                    max-width: 500px;
                    width: 100%;
                    background: rgba(255, 255, 255, 0.95);
                    backdrop-filter: blur(10px);
                    border-radius: 20px;
                    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
                    overflow: hidden;
                    animation: slideUp 0.6s ease-out;
                }}
                @keyframes slideUp {{
                    from {{
                        opacity: 0;
                        transform: translateY(30px);
                    }}
                    to {{
                        opacity: 1;
                        transform: translateY(0);
                    }}
                }}
                .header {{
                    background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
                    color: white;
                    padding: 30px 20px;
                    text-align: center;
                    position: relative;
                }}
                .success-icon {{
                    width: 80px;
                    height: 80px;
                    background: rgba(255, 255, 255, 0.2);
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin: 0 auto 15px;
                    font-size: 40px;
                    animation: bounce 1s ease-in-out;
                }}
                @keyframes bounce {{
                    0%, 20%, 50%, 80%, 100% {{
                        transform: translateY(0);
                    }}
                    40% {{
                        transform: translateY(-10px);
                    }}
                    60% {{
                        transform: translateY(-5px);
                    }}
                }}
                .header h1 {{
                    font-size: 24px;
                    font-weight: 600;
                    margin-bottom: 8px;
                }}
                .header .subtitle {{
                    font-size: 16px;
                    opacity: 0.9;
                }}
                .content {{
                    padding: 30px 25px;
                }}
                .info-card {{
                    background: #f8f9fa;
                    border-radius: 12px;
                    padding: 20px;
                    margin-bottom: 20px;
                    border-left: 4px solid #4CAF50;
                    text-align: center;
                }}
                .validity-info {{
                    color: #495057;
                    font-size: 16px;
                    font-weight: 600;
                }}
                .highlight {{
                    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
                    color: white;
                    padding: 15px;
                    border-radius: 10px;
                    text-align: center;
                    margin: 20px 0;
                    font-weight: 600;
                    box-shadow: 0 4px 15px rgba(238, 90, 36, 0.3);
                }}
                .benefits {{
                    background: linear-gradient(135deg, #74b9ff, #0984e3);
                    color: white;
                    padding: 20px;
                    border-radius: 12px;
                    margin-top: 20px;
                }}
                .benefits h3 {{
                    margin-bottom: 15px;
                    font-size: 18px;
                }}
                .benefit-item {{
                    display: flex;
                    align-items: center;
                    margin-bottom: 10px;
                    font-size: 14px;
                }}
                .benefit-item:before {{
                    content: "✓";
                    background: rgba(255, 255, 255, 0.2);
                    border-radius: 50%;
                    width: 20px;
                    height: 20px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin-right: 10px;
                    font-weight: bold;
                }}
                .success {{
                    background: linear-gradient(135deg, #28a745, #20c997);
                    color: white;
                    padding: 25px;
                    border-radius: 15px;
                    text-align: center;
                    margin: 20px 0;
                    font-size: 18px;
                    font-weight: 600;
                    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.3);
                }}
                @media (max-width: 480px) {{
                    .container {{
                        margin: 10px;
                        border-radius: 15px;
                    }}
                    .header {{
                        padding: 25px 15px;
                    }}
                    .content {{
                        padding: 25px 20px;
                    }}
                }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <div class="success-icon">✓</div>
                    <h1>{标题}</h1>
                    <div class="subtitle">{消息}</div>
                </div>
                <div class="content">
        """

        # 如果有订单信息，检查是否为代理服务器支付
        if 订单信息:
            程序名 = 订单信息.get("程序名", "")

            # 如果是代理服务器支付，显示简化的成功信息
            if 程序名 == "代理服务器":
                html += '''
                <div class="success">
                    🎉 支付成功！<br>
                    请回到软件再次勾选即可激活
                </div>
                '''
            else:
                # 普通支付显示详细信息
                # 获取有效期信息
                有效期 = 订单信息.get("有效期", "N/A")

                # 前台统一显示无限额度
                额度数量 = "无限"

                # 只显示有效期信息
                if 有效期 != "N/A":
                    html += f'''
                    <div class="info-card">
                        <div class="validity-info">服务有效期至：{有效期}</div>
                    </div>
                    '''

                # 显示感谢信息（前台显示无限额度）
                html += f'<div class="highlight">🎉 感谢您的支持！您已成功获得{额度数量}额度</div>'

                # 显示权益说明（前台显示无限额度）
                html += f'''
                <div class="benefits">
                    <h3>🚀 您已解锁权益</h3>
                    <div class="benefit-item">{额度数量}额度可一键获取无限次</div>

                </div>
                '''

        html += """
                </div>
            </div>
        </body>
        </html>
        """
        return html

def 注册支付路由(app, 支付系统实例=None, 数据库实例=None):
    """
    注册支付相关的路由到Flask应用

    参数:
        app: Flask应用实例
        支付系统实例: 支付宝支付系统实例（可选）
        数据库实例: 数据库管理器实例（可选）
    """
    支付处理 = 支付API处理(支付系统实例, 数据库实例)

    # 注册核心路由
    app.route('/create_payment', methods=['POST'])(支付处理.create_payment) # 创建支付链接
    app.route('/create_renewal_payment', methods=['POST'])(支付处理.create_renewal_payment) # 创建续卡支付链接
    app.route('/create_proxy_payment', methods=['POST'])(支付处理.create_proxy_payment) # 创建代理服务器支付链接
    app.route('/alipay_notify', methods=['POST'])(支付处理.alipay_notify) # 支付宝异步通知
    app.route('/query_order', methods=['POST'])(支付处理.query_order) # 查询订单
    app.route('/payment_result', methods=['GET'])(支付处理.payment_result) # 支付结果

    return 支付处理

# 添加主程序入口，支持独立运行
if __name__ == '__main__':
    from flask import Flask

    # 创建Flask应用
    app = Flask(__name__)

    # 创建支付系统和数据库实例
    支付系统实例 = 支付宝支付系统()
    数据库实例 = 数据库管理器()

    # 更新支付宝配置中的回调URL
    Config.支付宝配置['return_url'] = f"{Config.get_public_url()}/payment_result"
    Config.支付宝配置['notify_url'] = f"{Config.get_public_url()}/alipay_notify"

    # 注册支付相关路由
    注册支付路由(app, 支付系统实例, 数据库实例)

    # 获取服务配置
    服务配置 = Config.服务配置

    # 输出简化的系统信息和日志记录
    启动信息 = f"卡密支付系统启动 - 访问地址: http://{服务配置['host']}:{服务配置['port']}"
    管理页面信息 = f"管理员卡密生成页面: http://{服务配置['host']}:{服务配置['port']}/admin/cards"

    print(启动信息)
    print(管理页面信息)

    # 记录服务启动日志
    logging.info(启动信息)
    logging.info(管理页面信息)
    logging.info("支付系统日志轮转已启用 - 文件: 支付系统.log, 最大大小: 10MB, 备份数量: 5")
    logging.info("IP访问管理器已启动 - 同秒访问阈值: 5次, 永久拉黑机制已启用")

    # 启动Flask应用
    app.run(
        host=服务配置['host'],
        port=服务配置['port'],
        debug=服务配置['debug']
    )